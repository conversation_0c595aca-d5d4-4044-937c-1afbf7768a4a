/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server_types.go
 *
 * DESCRIPTION :    VPN server model definitions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package server

import (
	"time"
)

/*****************************************************************************
 * NAME: Server
 *
 * DESCRIPTION:
 *     Represents a VPN server with connection information, status, and
 *     performance metrics
 *
 * FIELDS:
 *     ID         - Unique server identifier
 *     Name       - Server display name
 *     NameEn     - Server English name
 *     ServerName - Server address (domain or IP)
 *     ServerPort - Server port number
 *     IsAuto     - Whether this is an auto-routing server
 *     Ping       - Latency in milliseconds
 *     Status     - Server status ("online", "offline", "unknown")
 *     LastCheck  - Last status check timestamp
 *****************************************************************************/
type Server struct {
	ID         string    // Unique server identifier
	Name       string    // Server display name
	NameEn     string    // Server English name
	ServerName string    // Server address (domain or IP)
	ServerPort int       // Server port number
	IsAuto     bool      // Whether this is an auto-routing server
	Ping       int       // Latency in milliseconds
	Status     string    // Server status ("online", "offline", "unknown")
	LastCheck  time.Time // Last status check timestamp
}

/*****************************************************************************
 * NAME: ServerList
 *
 * DESCRIPTION:
 *     Represents a server list response containing version information
 *     and the list of available servers
 *
 * FIELDS:
 *     Version    - Server list version
 *     ServerList - List of available servers
 *****************************************************************************/
type ServerList struct {
	Version    string   `json:"version"`    // Server list version
	ServerList []Server `json:"serverlist"` // List of available servers
}

/*****************************************************************************
 * NAME: ServerPerformance
 *
 * DESCRIPTION:
 *     Represents a server performance record for tracking latency
 *     and connection success metrics
 *
 * FIELDS:
 *     ServerID  - Server identifier
 *     Timestamp - Record timestamp
 *     Ping      - Latency in milliseconds
 *     Success   - Whether the connection was successful
 *****************************************************************************/
type ServerPerformance struct {
	ServerID  string    // Server identifier
	Timestamp time.Time // Record timestamp
	Ping      int       // Latency in milliseconds
	Success   bool      // Whether the connection was successful
}

/*****************************************************************************
 * NAME: Config
 *
 * DESCRIPTION:
 *     Configuration for server manager including update intervals,
 *     timeouts, and monitoring settings
 *
 * FIELDS:
 *     ServerListURL          - Server list URL
 *     ServerListFile         - Server list file path
 *     DefaultServerFile      - Default server file path
 *     UpdateInterval         - Server list update interval
 *     PingInterval           - Ping operation interval
 *     PingTimeout            - Ping operation timeout
 *     ServerOfflineThreshold - Server offline threshold
 *     MaxReconnectAttempts   - Maximum reconnection attempts
 *     ReconnectDelay         - Reconnection delay
 *     NetworkMonitorInterval - Network interface monitoring interval
 *****************************************************************************/
type Config struct {
	ServerListURL          string        // Server list URL
	ServerListFile         string        // Server list file path
	DefaultServerFile      string        // Default server file path
	UpdateInterval         time.Duration // Server list update interval
	PingInterval           time.Duration // Ping operation interval
	PingTimeout            time.Duration // Ping operation timeout
	ServerOfflineThreshold time.Duration // Server offline threshold
	MaxReconnectAttempts   int           // Maximum reconnection attempts
	ReconnectDelay         time.Duration // Reconnection delay
	NetworkMonitorInterval time.Duration // Network interface monitoring interval
	TLSSkipVerify          bool          // Skip TLS certificate verification for HTTPS requests
}

/*****************************************************************************
 * NAME: DefaultConfig
 *
 * DESCRIPTION:
 *     Returns default configuration for server manager with standard
 *     intervals and timeouts
 *
 * RETURNS:
 *     Config - Default configuration instance
 *****************************************************************************/
func DefaultConfig() Config {
	return Config{
		ServerListURL:          "", // No default URL - should be set by UI
		ServerListFile:         "",
		DefaultServerFile:      "",
		UpdateInterval:         1 * time.Hour,
		PingInterval:           25 * time.Second, // Modified to 25 seconds
		PingTimeout:            2000 * time.Millisecond,
		ServerOfflineThreshold: 15 * time.Minute,
		MaxReconnectAttempts:   5,
		ReconnectDelay:         5 * time.Second,
		NetworkMonitorInterval: 10 * time.Second, // Network interface monitoring interval, default 10 seconds
		TLSSkipVerify:          true,             // Default to skip verification for compatibility
	}
}
