/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      main.go
 *
 * DESCRIPTION :    Main entry point for the Panabit Client WAN service. This service
 *                  provides VPN connectivity through TUN interface, manages
 *                  server connections, and exposes HTTP/WebSocket APIs for
 *                  client applications.
 *
 * AUTHOR :         wei
 *
 * CREATED :        2025-01-16
 *
 * HISTORY :        2025-01-16  Initial creation with standard comment format
 *                  2025-01-16  Code standardization and cleanup
 ******************************************************************************/

package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"path/filepath"
	"runtime/debug"
	"syscall"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/logger"
	"mobile/internal/connection/manager"
	"mobile/internal/connection/server"
	"mobile/internal/platform/tun"
	"mobile/internal/service/config"
	"mobile/internal/service/http"
	"mobile/internal/service/websocket"
)

// Application constants
const (
	// Server manager configuration
	defaultServerUpdateInterval   = 30 * time.Minute
	defaultPingInterval           = 25 * time.Second
	defaultPingTimeout            = 2000 * time.Millisecond
	defaultNetworkMonitorInterval = 20 * time.Second

	// WebSocket configuration
	defaultHeartbeatInterval = 30 * time.Second

	// Server timeout configuration
	defaultReadTimeout     = 15 * time.Second
	defaultWriteTimeout    = 15 * time.Second
	defaultShutdownTimeout = 5 * time.Second

	// Connection manager configuration
	defaultConnectionTimeout = 30 * time.Second
	defaultRetryCount        = 3
	defaultRetryInterval     = 5 * time.Second
	defaultGracefulShutdown  = 10 * time.Second

	// WebSocket port offset
	websocketPortOffset = 1
)

// Version information
var (
	// version variable will be injected at build time via -ldflags
	// Format: 1.0.0-short-commit-date
	version    = "1.1.0"
	configPath = flag.String("config", "config.yaml", "Path to configuration file")
)

/*****************************************************************************
 * NAME: getBuildInfo
 *
 * DESCRIPTION:
 *     Retrieves build information from runtime debug info.
 *
 * RETURNS:
 *     string - Build information string
 *****************************************************************************/
func getBuildInfo() string {
	if info, ok := debug.ReadBuildInfo(); ok {
		return info.GoVersion
	}
	return "unknown"
}

/*****************************************************************************
 * NAME: handlePanic
 *
 * DESCRIPTION:
 *     Handles panic recovery with logging and graceful exit.
 *
 * PARAMETERS:
 *     log    - Logger instance for panic logging
 *     module - Module name for context
 *
 * RETURNS:
 *     None (exits the program)
 *****************************************************************************/
func handlePanic(log logger.Logger, module string) {
	if r := recover(); r != nil {
		log.Error(fmt.Sprintf("%s panic", module),
			logger.Any("panic", r),
			logger.String("stack", string(debug.Stack())),
		)
		// Log to global panic logger
		errors.LogPanic(log, r)
		os.Exit(1)
	}
}

/*****************************************************************************
 * NAME: getEncryptionType
 *
 * DESCRIPTION:
 *     Determines encryption type based on configuration.
 *
 * PARAMETERS:
 *     encrypt - Boolean indicating if encryption is enabled
 *
 * RETURNS:
 *     uint8 - Encryption type (0 for none, 1 for XOR)
 *****************************************************************************/
func getEncryptionType(encrypt bool) uint8 {
	if encrypt {
		return 1 // Use XOR encryption (EncryptionXOR)
	}
	return 0 // No encryption (EncryptionNone)
}

/*****************************************************************************
 * NAME: initializeLogger
 *
 * DESCRIPTION:
 *     Initializes the logging system with configuration and panic reporting.
 *
 * PARAMETERS:
 *     cfg - Application configuration
 *
 * RETURNS:
 *     logger.Logger - Initialized logger instance
 *     error         - Error if initialization fails
 *****************************************************************************/
func initializeLogger(cfg *config.Config) (logger.Logger, error) {
	// Create logger configuration
	logConfig := logger.Config{
		Level:  logger.Level(cfg.Logging.Level),
		Format: logger.FormatJSON,
		Outputs: []logger.Output{
			{
				Type:       logger.TypeFile,
				File:       cfg.Logging.File,
				MaxSize:    cfg.Logging.MaxSize,
				MaxBackups: cfg.Logging.MaxBackups,
				MaxAge:     cfg.Logging.MaxAge,
			},
			{
				Type: logger.TypeConsole,
			},
		},
		IncludeCaller:   true,
		StacktraceLevel: logger.LevelError,
	}

	log, err := logger.New(logConfig)
	if err != nil {
		return nil, err
	}

	log.Info("Starting Panabit Client service",
		logger.String("version", version),
		logger.String("build_info", getBuildInfo()))
	log.Info("Configuration loaded successfully",
		logger.String("config_path", *configPath))

	// Initialize panic logger
	// Create panic report directory
	panicReportPath := filepath.Join(filepath.Dir(cfg.Logging.File), "panic_reports")
	errors.InitPanicLogger(log, panicReportPath, version)
	log.Info("Panic logger initialized",
		logger.String("report_path", panicReportPath),
		logger.String("version", version),
	)

	return log, nil
}

/*****************************************************************************
 * NAME: initializeServerManager
 *
 * DESCRIPTION:
 *     Initializes and starts the server manager with configuration.
 *
 * PARAMETERS:
 *     cfg - Application configuration
 *     log - Logger instance
 *
 * RETURNS:
 *     *server.Manager - Initialized server manager
 *     error           - Error if initialization fails
 *****************************************************************************/
func initializeServerManager(cfg *config.Config, log logger.Logger) (*server.Manager, error) {
	// Create server manager configuration
	serverConfig := server.Config{
		ServerListURL:          cfg.VPN.ServerListURL,
		ServerListFile:         cfg.VPN.ServerListFile,
		UpdateInterval:         defaultServerUpdateInterval,
		PingInterval:           defaultPingInterval,
		PingTimeout:            defaultPingTimeout,
		NetworkMonitorInterval: defaultNetworkMonitorInterval,
		TLSSkipVerify:          cfg.VPN.TLS.SkipVerify,
	}

	// Check server list configuration
	if serverConfig.ServerListURL == "" && serverConfig.ServerListFile == "" {
		log.Warn("No server list URL or file configured, using default file path",
			logger.String("default_file", "servers.json"))
		serverConfig.ServerListFile = "servers.json"
	}

	log.Info("Creating server manager with configuration",
		logger.String("server_list_url", serverConfig.ServerListURL),
		logger.String("server_list_file", serverConfig.ServerListFile),
		logger.Duration("update_interval", serverConfig.UpdateInterval),
		logger.Duration("ping_interval", serverConfig.PingInterval),
		logger.Duration("ping_timeout", serverConfig.PingTimeout),
		logger.Bool("tls_skip_verify", serverConfig.TLSSkipVerify))

	serverManager := server.NewManager(serverConfig, log)

	// Initialize server manager
	log.Info("Initializing server manager")
	if err := serverManager.Initialize(); err != nil {
		log.Error("Failed to initialize server manager", logger.ErrorField(err))
		return nil, err
	}
	log.Info("Server manager initialized successfully")

	// Start server manager
	log.Info("Starting server manager")
	if err := serverManager.Start(); err != nil {
		log.Error("Failed to start server manager", logger.ErrorField(err))
		return nil, err
	}
	log.Info("Server manager started successfully")

	return serverManager, nil
}

/*****************************************************************************
 * NAME: initializeWebSocketComponents
 *
 * DESCRIPTION:
 *     Initializes WebSocket handler and server components.
 *
 * PARAMETERS:
 *     cfg           - Application configuration
 *     serverManager - Server manager instance
 *     log           - Logger instance
 *
 * RETURNS:
 *     *websocket.Handler - WebSocket handler
 *     *websocket.Server  - WebSocket server
 *****************************************************************************/
func initializeWebSocketComponents(cfg *config.Config, serverManager *server.Manager, log logger.Logger) (*websocket.Handler, *websocket.Server) {
	// Create WebSocket configuration
	wsConfig := &websocket.HandlerConfig{
		HeartbeatInterval: defaultHeartbeatInterval,
	}
	wsServerConfig := &websocket.ServerConfig{
		Host:            cfg.API.Host,
		Port:            cfg.API.Port + websocketPortOffset,
		ReadTimeout:     defaultReadTimeout,
		WriteTimeout:    defaultWriteTimeout,
		ShutdownTimeout: defaultShutdownTimeout,
	}

	wsHandler := websocket.NewHandler(wsConfig, nil, serverManager, log.WithModule("websocket"))
	wsServer := websocket.NewServer(wsServerConfig, wsHandler, log.WithModule("websocket-server"))

	return wsHandler, wsServer
}

/*****************************************************************************
 * NAME: initializeTUNDevice
 *
 * DESCRIPTION:
 *     Initializes the TUN subsystem and creates a TUN device.
 *
 * PARAMETERS:
 *     log - Logger instance
 *
 * RETURNS:
 *     tun.Device - TUN device instance
 *     error      - Error if initialization fails
 *****************************************************************************/
func initializeTUNDevice(log logger.Logger) (tun.Device, error) {
	// Initialize TUN subsystem
	tun.Initialize(log.WithModule("tun"))
	// Create TUN device
	tunDevice, err := tun.CreateDevice()
	if err != nil {
		return nil, err
	}
	return tunDevice, nil
}

/*****************************************************************************
 * NAME: initializeConnectionManager
 *
 * DESCRIPTION:
 *     Initializes the connection manager with configuration.
 *
 * PARAMETERS:
 *     cfg           - Application configuration
 *     log           - Logger instance
 *     tunDevice     - TUN device instance
 *     serverManager - Server manager instance
 *
 * RETURNS:
 *     *manager.Manager - Connection manager instance
 *****************************************************************************/
func initializeConnectionManager(cfg *config.Config, log logger.Logger, tunDevice tun.Device, serverManager *server.Manager) *manager.Manager {
	// Create connection manager configuration
	connConfig := manager.Config{
		MTU:               cfg.VPN.Tunnel.MTU,
		Encryption:        getEncryptionType(cfg.VPN.Tunnel.Encrypt),
		Timeout:           defaultConnectionTimeout,
		RetryCount:        defaultRetryCount,
		RetryInterval:     defaultRetryInterval,
		HeartbeatInterval: defaultHeartbeatInterval,
	}

	return manager.NewManager(connConfig, log.WithModule("connection"), tunDevice, serverManager)
}

/*****************************************************************************
 * NAME: initializeHTTPServer
 *
 * DESCRIPTION:
 *     Initializes the HTTP server with configuration and dependencies.
 *
 * PARAMETERS:
 *     cfg               - Application configuration
 *     log               - Logger instance
 *     wsHandler         - WebSocket handler
 *     connectionManager - Connection manager instance
 *     serverManager     - Server manager instance
 *
 * RETURNS:
 *     *http.Server - HTTP server instance
 *****************************************************************************/
func initializeHTTPServer(cfg *config.Config, log logger.Logger, wsHandler *websocket.Handler, connectionManager *manager.Manager, serverManager *server.Manager) *http.Server {
	// Create HTTP server configuration
	httpConfig := &http.Config{
		Host:            cfg.API.Host,
		Port:            cfg.API.Port,
		ReadTimeout:     defaultReadTimeout,
		WriteTimeout:    defaultWriteTimeout,
		ShutdownTimeout: defaultShutdownTimeout,
	}

	httpServer := http.NewServer(httpConfig, log.WithModule("http"), wsHandler)
	httpServer.SetDependencies(&http.Dependencies{
		ConnectionManager: connectionManager,
		ServerManager:     serverManager,
	})

	return httpServer
}

/*****************************************************************************
 * NAME: startServers
 *
 * DESCRIPTION:
 *     Starts HTTP and WebSocket servers in separate goroutines.
 *
 * PARAMETERS:
 *     log       - Logger instance
 *     httpServer - HTTP server instance
 *     wsServer   - WebSocket server instance
 *
 * RETURNS:
 *     None
 *****************************************************************************/
func startServers(log logger.Logger, httpServer *http.Server, wsServer *websocket.Server) {
	// Start HTTP server in a goroutine
	go func() {
		defer handlePanic(log, "HTTP server")
		if err := httpServer.Start(); err != nil {
			log.Error("HTTP server error", logger.ErrorField(err))
			os.Exit(1)
		}
	}()

	// Start WebSocket server in a goroutine
	go func() {
		defer handlePanic(log, "WebSocket server")
		if err := wsServer.Start(); err != nil {
			log.Error("WebSocket server error", logger.ErrorField(err))
			os.Exit(1)
		}
	}()
}

/*****************************************************************************
 * NAME: performGracefulShutdown
 *
 * DESCRIPTION:
 *     Waits for shutdown signal and performs graceful shutdown of all components.
 *
 * PARAMETERS:
 *     log               - Logger instance
 *     connectionManager - Connection manager instance
 *     serverManager     - Server manager instance
 *     wsHandler         - WebSocket handler
 *     httpServer        - HTTP server instance
 *     wsServer          - WebSocket server instance
 *
 * RETURNS:
 *     None
 *****************************************************************************/
func performGracefulShutdown(log logger.Logger, connectionManager *manager.Manager, serverManager *server.Manager, wsHandler *websocket.Handler, httpServer *http.Server, wsServer *websocket.Server) {
	// Wait for interrupt signal
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	sig := <-sigCh
	log.Info("Received signal, shutting down", logger.String("signal", sig.String()))

	// Create a context with timeout for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), defaultGracefulShutdown)
	defer cancel()

	// Shutdown components in reverse dependency order
	// 1. First disconnect VPN connection
	log.Info("Disconnecting VPN connection")
	if err := connectionManager.Disconnect(); err != nil {
		log.Error("Failed to disconnect VPN connection",
			logger.ErrorField(err),
			logger.String("component", "connection_manager"))
		// Continue execution, don't interrupt shutdown process due to disconnect failure
	} else {
		log.Info("VPN connection disconnected successfully")
	}

	// 2. Stop server manager to clean up server routes
	log.Info("Stopping server manager")
	if err := serverManager.Stop(); err != nil {
		log.Error("Failed to stop server manager",
			logger.ErrorField(err),
			logger.String("component", "server_manager"))
	} else {
		log.Info("Server manager stopped successfully")
	}

	// 3. Stop WebSocket handler
	log.Info("Stopping WebSocket handler")
	wsHandler.Stop()
	log.Info("WebSocket handler stopped successfully")

	// 4. Stop HTTP server
	log.Info("Stopping HTTP server")
	if err := httpServer.Stop(ctx); err != nil {
		log.Error("Failed to stop HTTP server",
			logger.ErrorField(err),
			logger.String("component", "http_server"))
	} else {
		log.Info("HTTP server stopped successfully")
	}

	// 5. Stop WebSocket server
	log.Info("Stopping WebSocket server")
	if err := wsServer.Stop(ctx); err != nil {
		log.Error("Failed to stop WebSocket server",
			logger.ErrorField(err),
			logger.String("component", "websocket_server"))
	} else {
		log.Info("WebSocket server stopped successfully")
	}

	// 6. Ensure TUN resources are released
	log.Info("Shutting down TUN subsystem")
	if err := tun.Shutdown(); err != nil {
		log.Error("Failed to shutdown TUN subsystem",
			logger.ErrorField(err),
			logger.String("component", "tun_subsystem"))
	} else {
		log.Info("TUN subsystem shutdown successfully")
	}

	log.Info("Panabit Client WAN service stopped gracefully",
		logger.String("version", version),
		logger.String("signal", sig.String()))
}

/*****************************************************************************
 * NAME: realMain
 *
 * DESCRIPTION:
 *     Main application logic for the Panabit Client WAN service. Initializes all
 *     components, starts servers, and handles graceful shutdown.
 *
 * RETURNS:
 *     None (exits on error)
 *****************************************************************************/
func realMain() {
	// Parse command line arguments
	flag.Parse()

	// Load configuration
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logging system
	log, err := initializeLogger(cfg)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer log.Sync() // Ensure log synchronization

	// Initialize server manager
	serverManager, err := initializeServerManager(cfg, log)
	if err != nil {
		log.Error("Failed to initialize server manager", logger.ErrorField(err))
		os.Exit(1)
	}

	// Initialize WebSocket components
	wsHandler, wsServer := initializeWebSocketComponents(cfg, serverManager, log)

	// Initialize TUN device
	tunDevice, err := initializeTUNDevice(log)
	if err != nil {
		log.Error("Failed to initialize TUN device", logger.ErrorField(err))
		os.Exit(1)
	}

	// Initialize connection manager
	connectionManager := initializeConnectionManager(cfg, log, tunDevice, serverManager)

	// Set up component relationships
	connectionManager.SetStatusNotifier(wsHandler)
	wsHandler.SetConnectionManager(connectionManager)

	// Start connection manager
	log.Info("Starting connection manager")
	if err := connectionManager.Start(); err != nil {
		log.Error("Failed to start connection manager", logger.ErrorField(err))
		os.Exit(1)
	}
	log.Info("Connection manager started successfully")

	// Initialize and start HTTP server
	httpServer := initializeHTTPServer(cfg, log, wsHandler, connectionManager, serverManager)

	// Start WebSocket handler
	wsHandler.Start()

	// Start servers
	startServers(log, httpServer, wsServer)

	log.Info("All servers are running successfully",
		logger.String("http_address", fmt.Sprintf("%s:%d", cfg.API.Host, cfg.API.Port)),
		logger.String("websocket_address", fmt.Sprintf("%s:%d", cfg.API.Host, cfg.API.Port+websocketPortOffset)))
	log.Info("HTTP API is available for UI requests")
	log.Info("WebSocket is available for real-time updates")

	// Wait for shutdown signal and perform graceful shutdown
	performGracefulShutdown(log, connectionManager, serverManager, wsHandler, httpServer, wsServer)
}

/*****************************************************************************
 * NAME: main
 *
 * DESCRIPTION:
 *     Entry point for the Panabit Client WAN service. Sets up panic recovery and
 *     calls the main application logic.
 *
 * RETURNS:
 *     None (exits on panic or completion)
 *****************************************************************************/
func main() {
	// Set up panic logging
	defer func() {
		if r := recover(); r != nil {
			// Use default logger to record panic
			errors.LogPanic(logger.Default(), r)
			// Don't recover execution, let program exit normally
			os.Exit(1)
		}
	}()

	// Execute actual main function logic
	realMain()
}
