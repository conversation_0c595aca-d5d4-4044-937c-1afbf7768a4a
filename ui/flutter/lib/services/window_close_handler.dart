// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      window_close_handler.dart
//  *
//  * DESCRIPTION :    窗口关闭处理器，负责处理窗口关闭事件和应用退出流程，
//  *                  确保资源正确清理和连接安全断开
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/

import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';

import '../core/app_state.dart';
import '../core/dependency_injection.dart';
import '../services/api_service.dart';
import '../services/log_service.dart';
import '../services/tray_service.dart';

import '../utils/constants.dart';

/// WindowCloseHandler
///
/// PURPOSE:
///     窗口关闭处理器，负责处理窗口关闭事件和应用退出流程
///
/// FEATURES:
///     - 窗口事件监听：监听窗口关闭事件
///     - 退出流程管理：执行完整的应用退出流程
///     - 连接断开：安全断开VPN连接
///     - 资源清理：清理应用资源和服务
///     - 错误处理：处理退出过程中的异常情况
///     - 强制退出：在异常情况下强制退出应用
///
/// USAGE:
///     在主窗口创建时实例化并传入BuildContext，
///     在窗口销毁时调用dispose()方法清理监听器
class WindowCloseHandler with WindowListener {
  final LogService _logService = serviceLocator<LogService>();
  final ApiService _apiService = serviceLocator<ApiService>();
  final AppState _appState = serviceLocator<AppState>();
  final BuildContext context;

  /// 托盘服务实例（仅Windows平台使用）
  TrayService? _trayService;

  /// WindowCloseHandler构造函数
  ///
  /// DESCRIPTION:
  ///     创建窗口关闭处理器实例，注册窗口事件监听器
  ///
  /// PARAMETERS:
  ///     context - Flutter构建上下文，用于UI操作
  WindowCloseHandler({
    required this.context,
  }) {
    windowManager.addListener(this);

    // 在Windows平台上尝试获取托盘服务
    if (Platform.isWindows) {
      try {
        _trayService = serviceLocator<TrayService>();
      } catch (e) {
        _logService.warning('WindowCloseHandler', 'TrayService not available: $e');
      }
    }
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     释放资源，移除窗口事件监听器
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void dispose() {
    windowManager.removeListener(this);
  }

  /// onWindowClose
  ///
  /// DESCRIPTION:
  ///     窗口关闭事件处理器，在Windows平台隐藏到托盘，其他平台执行完整退出流程
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  ///
  /// THROWS:
  ///     Exception - 退出过程中发生错误时强制退出应用
  @override
  void onWindowClose() async {
    _logService.info('WindowCloseHandler', 'Window close event received');

    try {
      // 在Windows平台且托盘服务可用时，隐藏到托盘而不是退出
      if (Platform.isWindows && _trayService != null) {
        _logService.info('WindowCloseHandler', 'Hiding window to system tray');
        await _trayService!.hideWindow();
        return;
      }

      // 其他平台或托盘服务不可用时，执行完整的退出流程
      _logService.info('WindowCloseHandler', 'Executing full exit flow');
      await handleExit();
    } catch (e) {
      _logService.error('WindowCloseHandler', 'Error handling window close event', e);

      // 如果出错，强制退出应用程序
      _logService.info('WindowCloseHandler', 'Exit process exception, force exiting application');
      exit(1);
    }
  }

  /// handleExit
  ///
  /// DESCRIPTION:
  ///     处理应用退出操作，使用与右上角关闭相同的逻辑确保一致性
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 退出过程中发生错误时记录日志并强制退出
  Future<void> handleExit() async {
    _logService.info('WindowCloseHandler', 'User chose to exit');

    try {
      // 1. 如果已连接，先断开连接
      if (_appState.connectionStatus == ConnectionStatus.connected) {
        _logService.info('WindowCloseHandler', 'Disconnecting WAN...');

        try {
          // 断开WAN连接
          await _apiService.disconnect();
          _logService.info('WindowCloseHandler', 'WAN disconnected successfully');
        } catch (e) {
          _logService.error('WindowCloseHandler', 'Failed to disconnect WAN', e);
        }
      }

      // 2. 发送HTTP关闭请求给后端
      _logService.info('WindowCloseHandler', 'Sending backend shutdown request...');
      try {
        final result = await _apiService.shutdownBackend();
        _logService.info('WindowCloseHandler', 'Backend shutdown request sent successfully: $result');
      } catch (e) {
        _logService.info('WindowCloseHandler', 'Backend shutdown request failed: $e');
      }

      // 3. 关闭窗口
      _logService.info('WindowCloseHandler', 'Preparing to close window...');
      try {
        // 强制关闭窗口
        _logService.info('WindowCloseHandler', 'Setting preventClose to false');
        await windowManager.setPreventClose(false);
        _logService.info('WindowCloseHandler', 'Calling windowManager.destroy()');
        await windowManager.destroy();
        _logService.info('WindowCloseHandler', 'Window destruction completed');
      } catch (e) {
        _logService.error('WindowCloseHandler', 'Failed to destroy window', e);
      }

      // 4. 退出应用
      _logService.info('WindowCloseHandler', 'Force exiting application');
      exit(0);
    } catch (e) {
      _logService.error('WindowCloseHandler', 'Error occurred while exiting application', e);
      // 即使出错，也强制退出
      exit(1);
    }
  }
}
