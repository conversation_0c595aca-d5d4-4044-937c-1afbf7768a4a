// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Initial implementation of iOS/macOS Platform Channel API service
//  */

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'cross_platform_api_service.dart';
import '../../models/connection_status.dart';
import '../../models/server.dart';
import '../../models/user_info.dart';
import '../../models/routing_settings.dart';
import '../../models/interface_info.dart';
import '../../utils/api_exception.dart';

/// PlatformChannelApiService
///
/// PURPOSE:
///     iOS/macOS implementation of CrossPlatformApiService using Platform Channels.
///     Communicates with Swift VPN services through Method Channel and Event Channel.
///     Replaces HTTP/WebSocket communication used in Windows/Linux platforms.
///
/// FEATURES:
///     - Method Channel for synchronous API calls
///     - Event Channel for real-time status updates
///     - Automatic Swift backend initialization
///     - Platform-native error handling
///     - Background execution support
///
/// USAGE:
///     Created by PlatformServiceFactory for iOS/macOS platforms.
///     Provides same interface as HttpApiService but uses Platform Channels.
class PlatformChannelApiService implements CrossPlatformApiService {
  
  // ============================================================================
  // PLATFORM CHANNEL CONFIGURATION
  // ============================================================================
  
  static const MethodChannel _methodChannel = MethodChannel('panabit_client/methods');
  static const EventChannel _eventChannel = EventChannel('panabit_client/events');
  
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  bool _isInitialized = false;
  StreamController<Map<String, dynamic>>? _eventController;
  StreamSubscription? _eventSubscription;

  // 保存最后一次登录响应以便提取best_server
  Map<String, dynamic>? _lastLoginResponse;
  
  // ============================================================================
  // INITIALIZATION AND LIFECYCLE
  // ============================================================================
  
  @override
  Future<bool> initialize() async {
    if (_isInitialized) {
      return true;
    }

    try {
      // Initialize event stream controller
      _eventController = StreamController<Map<String, dynamic>>.broadcast();

      // ✅ 重要：EventChannel监听延迟到后端服务启动后
      // 这样可以避免在PlatformChannelHandler未创建时就尝试监听事件
      debugPrint('PlatformChannelApiService: Initialized (EventChannel listening will start after backend initialization)');

      _isInitialized = true;
      return true;

    } catch (e) {
      throw ApiException(
        'Failed to initialize Platform Channel API service: $e',
        1000,
        'initialization_failed'
      );
    }
  }

  /// _ensureEventListening
  ///
  /// DESCRIPTION:
  ///     确保EventChannel监听已启动，在第一次使用时自动启动
  ///
  /// RETURNS:
  ///     void
  void _ensureEventListening() {
    if (_eventSubscription == null && _isInitialized) {
      debugPrint('PlatformChannelApiService: Auto-starting EventChannel listening...');
      try {
        _setupEventListening();
      } catch (e) {
        debugPrint('PlatformChannelApiService: Failed to start EventChannel listening: $e');
        // 不抛出异常，允许应用继续运行
      }
    }
  }
  
  @override
  Future<void> dispose() async {
    await _eventSubscription?.cancel();
    await _eventController?.close();
    _eventController = null;
    _eventSubscription = null;
    _isInitialized = false;
  }
  
  /// _setupEventListening
  ///
  /// DESCRIPTION:
  ///     Setup Event Channel listening for real-time updates from Swift backend.
  ///
  /// RETURNS:
  ///     void
  void _setupEventListening() {
    _eventSubscription = _eventChannel.receiveBroadcastStream().listen(
      (event) {
        // Add special tracking for status events
    if (event['event'] == 'status') {
      // final status = event['data']?['status'];
      // final timestamp = DateTime.now().millisecondsSinceEpoch;
      // print('🚨 CONN_FAIL_DEBUG: EventChannel received status event - status: $status, timestamp: $timestamp');
    }
    // print('🔥🔥🔥 [PlatformChannelApiService] Received event from EventChannel: $event');

        // Convert event to Map<String, dynamic> if it's a Map
        if (event is Map) {
          try {
            final convertedEvent = _convertToStringDynamicMap(event);
            // print('🔥🔥🔥 [PlatformChannelApiService] Event converted successfully, adding to controller');
            _eventController?.add(convertedEvent);
          } catch (e) {
            // print('🔥🔥🔥 [PlatformChannelApiService] Failed to convert event: $e');
          }
        } else {
          // print('🔥🔥🔥 [PlatformChannelApiService] Event is not Map: ${event.runtimeType}');
        }
      },
      onError: (error) {
        // print('🔥🔥🔥 [PlatformChannelApiService] EventChannel error: $error');
        _eventController?.addError(error);
      },
    );
  }

  /// _convertToStringDynamicMap
  ///
  /// DESCRIPTION:
  ///     Recursively converts Map<Object?, Object?> to Map<String, dynamic>
  ///     to handle Platform Channel type conversion issues.
  ///
  /// PARAMETERS:
  ///     obj - Object to convert
  ///
  /// RETURNS:
  ///     Map<String, dynamic> - Converted map
  Map<String, dynamic> _convertToStringDynamicMap(dynamic obj) {
    if (obj is Map) {
      return obj.map((key, value) => MapEntry(
        key.toString(),
        _convertValue(value),
      ));
    }
    throw ArgumentError('Object is not a Map');
  }

  /// _convertValue
  ///
  /// DESCRIPTION:
  ///     Recursively converts values to appropriate types.
  ///
  /// PARAMETERS:
  ///     value - Value to convert
  ///
  /// RETURNS:
  ///     dynamic - Converted value
  dynamic _convertValue(dynamic value) {
    if (value is Map) {
      return _convertToStringDynamicMap(value);
    } else if (value is List) {
      return value.map((item) => _convertValue(item)).toList();
    } else {
      return value;
    }
  }
  
  // ============================================================================
  // AUTHENTICATION
  // ============================================================================
  
  @override
  Future<UserInfo> login(String username, String password) async {
    _ensureInitialized();
    _ensureEventListening(); // 确保EventChannel监听已启动

    try {
      debugPrint('PlatformChannelApiService: Starting login via Platform Channel');
      debugPrint('PlatformChannelApiService: Login parameters - username: $username');

      // Add timeout protection for Platform Channel call
      // Increased timeout to 15 seconds to accommodate ping operation during login
      debugPrint('PlatformChannelApiService: Calling method channel login with 15s timeout');
      final result = await _methodChannel.invokeMethod('login', {
        'username': username,
        'password': password,
      }).timeout(
        const Duration(seconds: 15), // 15 second timeout to accommodate ping operation
        onTimeout: () {
          debugPrint('PlatformChannelApiService: Login request timed out after 15 seconds');
          throw ApiException(
            'Login request timed out. Please check your network connection and try again.',
            1008, // Network timeout error code
            'network_timeout'
          );
        },
      );

      debugPrint('PlatformChannelApiService: Method channel login returned: $result');

      if (result != null && result is Map && result['success'] == true) {
        // 保存原始登录响应以便后续提取best_server
        _lastLoginResponse = _convertToStringDynamicMap(result);
        debugPrint('PlatformChannelApiService: Saved login response for best_server extraction');

        // Create UserInfo from username since backend returns server info, not user info
        return UserInfo(
          displayName: username,
          department: '',
          position: '',
        );
      } else {
        final errorMessage = (result is Map) ? result['message']?.toString() ?? 'Login failed' : 'Login failed';
        final errorCode = (result is Map) ? (result['code'] as int?) ?? 1001 : 1001;
        throw ApiException(
          errorMessage,
          errorCode,
          'authentication_failed'
        );
      }
    } on PlatformException catch (e) {
      throw ApiException(
        'Platform Channel login error: ${e.message}',
        int.tryParse(e.code) ?? 1014, // authenticationFailed
        'platform_error'
      );
    }
  }
  
  @override
  Future<void> logout() async {
    _ensureInitialized();

    // Note: Flutter UI logout is handled locally by AuthService
    // No Platform Channel call needed for logout operation
    // This method is kept for interface compatibility but does nothing
  }
  
  // ============================================================================
  // SERVER MANAGEMENT
  // ============================================================================
  
  @override
  Future<List<Server>> getServers() async {
    _ensureInitialized();

    try {
      final result = await _methodChannel.invokeMethod('getServers');

      if (result != null && result is Map && result['success'] == true) {
        final serverList = result['data'];
        if (serverList is List) {
          return serverList.map((serverData) {
            // Ensure serverData is properly cast to Map<String, dynamic>
            if (serverData is Map) {
              final serverMap = Map<String, dynamic>.from(serverData);
              return Server.fromJson(serverMap);
            } else {
              throw ApiException('Invalid server data format', 1003, 'invalid_server_data');
            }
          }).toList();
        }
      }
      throw ApiException('Invalid server list response', 1003, 'invalid_response');
    } on PlatformException catch (e) {
      throw ApiException(
        'Platform Channel getServers error: ${e.message}',
        int.tryParse(e.code) ?? 1003,
        'platform_error'
      );
    }
  }
  
  @override
  Future<int> pingServer(String serverId) async {
    _ensureInitialized();

    try {
      final result = await _methodChannel.invokeMethod('pingServer', {
        'server_id': serverId,
      });

      return result as int? ?? -1;
    } on PlatformException {
      // Return -1 for ping failures instead of throwing
      return -1;
    }
  }

  @override
  Future<void> pingServers() async {
    _ensureInitialized();
    _ensureEventListening(); // 确保EventChannel监听已启动以接收ping结果

    try {
      await _methodChannel.invokeMethod('pingServers');
    } on PlatformException catch (e) {
      throw ApiException(
        'Platform Channel pingServers error: ${e.message}',
        int.tryParse(e.code) ?? 1017, // networkTimeout
        'platform_error'
      );
    }
  }
  
  // ============================================================================
  // VPN PERMISSION MANAGEMENT
  // ============================================================================

  /// checkVPNPermission
  ///
  /// DESCRIPTION:
  ///     检查VPN权限状态
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>> - 权限状态信息
  Future<Map<String, dynamic>> checkVPNPermission() async {
    _ensureInitialized();

    try {
      final result = await _methodChannel.invokeMethod('checkVPNPermission');
      return Map<String, dynamic>.from(result);
    } on PlatformException catch (e) {
      throw ApiException(
        'Platform Channel checkVPNPermission error: ${e.message}',
        int.tryParse(e.code) ?? 1019, // permission error
        'platform_error'
      );
    }
  }

  /// requestVPNPermission
  ///
  /// DESCRIPTION:
  ///     请求VPN权限
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>> - 权限请求结果
  Future<Map<String, dynamic>> requestVPNPermission() async {
    _ensureInitialized();

    try {
      final result = await _methodChannel.invokeMethod('requestVPNPermission');
      return Map<String, dynamic>.from(result);
    } on PlatformException catch (e) {
      throw ApiException(
        'Platform Channel requestVPNPermission error: ${e.message}',
        int.tryParse(e.code) ?? 1019, // permission error
        'platform_error'
      );
    }
  }

  // ============================================================================
  // VPN CONNECTION CONTROL
  // ============================================================================

  @override
  Future<Map<String, dynamic>?> connect(String serverId, String username, String password) async {
    _ensureInitialized();
    _ensureEventListening(); // 确保EventChannel监听已启动以接收连接状态事件

    try {
      // VPN权限检查只在Android平台使用，iOS平台在连接时自动处理权限
      if (Platform.isAndroid) {
        // 1. 首先检查VPN权限状态
        debugPrint('PlatformChannelApiService: Checking VPN permission before connection (Android)');
        final permissionStatus = await checkVPNPermission();

        if (!(permissionStatus['granted'] as bool? ?? false)) {
          // 2. 权限未授权，请求权限
          debugPrint('PlatformChannelApiService: VPN permission not granted, requesting permission (Android)');
          final permissionRequest = await requestVPNPermission();

          if (!(permissionRequest['granted'] as bool? ?? false)) {
            // 检查是否需要用户操作
            final requiresUserAction = permissionRequest['requires_user_action'] as bool? ?? false;
            final message = permissionRequest['message'] as String? ?? 'VPN权限被拒绝';

            if (requiresUserAction) {
              // 权限请求已启动，需要用户操作
              throw ApiException(
                message,
                1020, // permission request launched, user action required
                'vpn_permission_user_action_required'
              );
            } else {
              // 权限被拒绝
              throw ApiException(
                message,
                1019, // permission error
                'vpn_permission_denied'
              );
            }
          }
        }

        debugPrint('PlatformChannelApiService: VPN permission granted, proceeding with connection (Android)');
      } else {
        // iOS/macOS平台：跳过权限检查，直接连接（权限在连接时自动处理）
        debugPrint('PlatformChannelApiService: Proceeding with connection (iOS/macOS - no explicit permission check needed)');
      }

      // 进行连接，传递用户名和密码
      debugPrint('PlatformChannelApiService: Calling method channel connect with credentials and 30s timeout');
      final result = await _methodChannel.invokeMethod('connect', {
        'server_id': serverId,
        'username': username,
        'password': password, // iOS/Android平台传递明文密码
      }).timeout(
        const Duration(seconds: 30), // 30 second timeout for connection
        onTimeout: () {
          debugPrint('PlatformChannelApiService: Connect request timed out after 30 seconds');
          throw ApiException(
            'Connection request timed out. Please check your network connection and try again.',
            1008, // Network timeout error code
            'network_timeout'
          );
        },
      );

      if (result != true) {
        throw ApiException(
          'Connection failed',
          1004,
          'connection_failed'
        );
      }

      // iOS/Android平台通过事件推送interface信息，这里返回null
      return null;
    } on PlatformException catch (e) {
      throw ApiException(
        'Platform Channel connect error: ${e.message}',
        int.tryParse(e.code) ?? 1003, // connectionManagerError
        'platform_error'
      );
    }
  }
  
  @override
  Future<void> disconnect() async {
    _ensureInitialized();
    
    try {
      await _methodChannel.invokeMethod('disconnect');
    } on PlatformException catch (e) {
      throw ApiException(
        'Platform Channel disconnect error: ${e.message}',
        int.tryParse(e.code) ?? 1005,
        'platform_error'
      );
    }
  }
  
  @override
  Future<void> reconnect() async {
    _ensureInitialized();

    // Note: Reconnect is handled internally by backend auto-reconnection logic
    // Flutter UI doesn't directly call reconnect - it uses disconnect + connect
    // This method is kept for interface compatibility but throws not supported
    throw ApiException(
      'Reconnect not supported - use disconnect() then connect(serverId)',
      1006,
      'not_supported'
    );
  }



  // ============================================================================
  // STATUS AND MONITORING
  // ============================================================================
  
  @override
  Future<ConnectionStatus> getConnectionStatus() async {
    _ensureInitialized();
    
    try {
      final result = await _methodChannel.invokeMethod('getStatus');

      if (result != null && result is Map && result['success'] == true) {
        final statusData = result['data'];
        if (statusData != null) {
          // Ensure statusData is properly cast to Map<String, dynamic>
          if (statusData is Map) {
            final statusMap = Map<String, dynamic>.from(statusData);
            return _parseConnectionStatus(statusMap);
          } else {
            return ConnectionStatus.disconnected;
          }
        }
      }
      return ConnectionStatus.disconnected;
    } on PlatformException {
      // Return disconnected status for errors instead of throwing
      return ConnectionStatus.disconnected;
    }
  }
  
  @override
  Future<InterfaceInfo> getInterfaceInfo() async {
    _ensureInitialized();

    try {
      final result = await _methodChannel.invokeMethod('getInterface');

      if (result != null && result is Map && result['success'] == true) {
        final interfaceData = result['data'];
        if (interfaceData != null) {
          // Ensure interfaceData is properly cast to Map<String, dynamic>
          if (interfaceData is Map) {
            final interfaceMap = Map<String, dynamic>.from(interfaceData);
            return InterfaceInfo.fromJson(interfaceMap);
          } else {
            throw ApiException('Invalid interface data format', 1007, 'invalid_interface_data');
          }
        }
      }
      throw ApiException('Invalid interface info response', 1007, 'invalid_response');
    } on PlatformException catch (e) {
      throw ApiException(
        'Platform Channel getInterfaceInfo error: ${e.message}',
        int.tryParse(e.code) ?? 1007, // networkExtensionError
        'platform_error'
      );
    }
  }
  
  @override
  Future<bool> healthCheck() async {
    if (!_isInitialized) {
      return false;
    }

    try {
      final result = await _methodChannel.invokeMethod('health');

      // Swift backend returns a structured response: {"success": true, "data": {...}}
      if (result is Map) {
        return result['success'] == true;
      }

      // Fallback for simple boolean response
      return result == true;
    } catch (e) {
      return false;
    }
  }
  
  // ============================================================================
  // ROUTING CONFIGURATION
  // ============================================================================
  
  @override
  Future<RoutingSettingsModel> getRoutingSettings() async {
    _ensureInitialized();

    try {
      final result = await _methodChannel.invokeMethod('getRoutingSettings');

      if (result != null) {
        final settings = RoutingSettingsModel();
        // Ensure result is properly cast to Map<String, dynamic>
        if (result is Map) {
          final settingsMap = Map<String, dynamic>.from(result);
          settings.fromJson(settingsMap);
        } else {
          throw ApiException('Invalid routing settings data format', 1008, 'invalid_settings_data');
        }
        return settings;
      } else {
        throw ApiException('Invalid routing settings response', 1008, 'invalid_response');
      }
    } on PlatformException catch (e) {
      throw ApiException(
        'Platform Channel getRoutingSettings error: ${e.message}',
        int.tryParse(e.code) ?? 1008,
        'platform_error'
      );
    }
  }

  @override
  Future<void> updateRoutingSettings(RoutingSettingsModel settings) async {
    _ensureInitialized();

    try {
      await _methodChannel.invokeMethod('setRoutingSettings', settings.toJson());
    } on PlatformException catch (e) {
      throw ApiException(
        'Platform Channel updateRoutingSettings error: ${e.message}',
        int.tryParse(e.code) ?? 1009,
        'platform_error'
      );
    }
  }

  @override
  Future<void> setServerProviderUrl(String url) async {
    _ensureInitialized();

    try {
      debugPrint('PlatformChannelApiService: Starting setServerProviderUrl');
      debugPrint('PlatformChannelApiService: URL parameter: $url');

      // Add timeout protection for Platform Channel call
      debugPrint('PlatformChannelApiService: Calling method channel setServerProviderUrl with 30s timeout');
      await _methodChannel.invokeMethod('setServerProviderUrl', {
        'url': url,
      }).timeout(
        const Duration(seconds: 30), // 30 second timeout for server URL setting
        onTimeout: () {
          debugPrint('PlatformChannelApiService: setServerProviderUrl request timed out after 30 seconds');
          throw ApiException(
            'Set server provider URL request timed out. Please check your network connection and try again.',
            1010, // Server provider URL timeout error code
            'network_timeout'
          );
        },
      );

      debugPrint('PlatformChannelApiService: setServerProviderUrl completed successfully');
    } on PlatformException catch (e) {
      debugPrint('PlatformChannelApiService: setServerProviderUrl PlatformException: ${e.code} - ${e.message}');

      // 将平台特定的错误码映射到统一的错误码体系
      int errorCode;
      String errorType;

      switch (e.code) {
        case 'HTTP_ERROR':
          // 解析HTTP状态码
          if (e.message?.contains('status code 404') == true) {
            errorCode = 1201; // 服务器列表未找到
            errorType = 'server_list_not_found';
          } else if (e.message?.contains('status code 408') == true ||
                     e.message?.contains('timeout') == true) {
            errorCode = 1203; // 服务器列表请求超时
            errorType = 'server_list_timeout';
          } else if (e.message?.contains('status code 500') == true ||
                     e.message?.contains('status code 502') == true ||
                     e.message?.contains('status code 503') == true) {
            errorCode = 1204; // 服务器列表网络错误
            errorType = 'server_list_network_error';
          } else {
            errorCode = 1204; // 默认为网络错误
            errorType = 'server_list_network_error';
          }
          break;
        case 'INVALID_ARGUMENTS':
          errorCode = 1202; // 无效的服务器列表格式
          errorType = 'server_list_invalid';
          break;
        default:
          errorCode = int.tryParse(e.code) ?? 1010;
          errorType = 'platform_error';
          break;
      }

      throw ApiException(
        'Platform Channel setServerProviderUrl error: ${e.message}',
        errorCode,
        errorType
      );
    }
  }
  
  // ============================================================================
  // EVENT STREAMING
  // ============================================================================
  
  @override
  Stream<Map<String, dynamic>> get eventStream {
    return _eventController?.stream ?? const Stream.empty();
  }
  
  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  @override
  Future<void> shutdown() async {
    // Platform Channel implementation doesn't need explicit shutdown
    // Swift backend lifecycle is managed by iOS/macOS system
    await dispose();
  }
  
  /// _ensureInitialized
  ///
  /// DESCRIPTION:
  ///     Ensure service is initialized before API calls.
  ///
  /// THROWS:
  ///     ApiException - if service is not initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw ApiException(
        'Platform Channel API service not initialized',
        1000,
        'not_initialized'
      );
    }
  }

  /// _parseConnectionStatus
  ///
  /// DESCRIPTION:
  ///     Parse connection status from Platform Channel response data.
  ///
  /// PARAMETERS:
  ///     data - response data containing status information
  ///
  /// RETURNS:
  ///     ConnectionStatus - parsed connection status
  ConnectionStatus _parseConnectionStatus(Map<String, dynamic> data) {
    final statusString = data['status']?.toString().toLowerCase();

    switch (statusString) {
      case 'connected':
        return ConnectionStatus.connected;
      case 'connecting':
        return ConnectionStatus.connecting;
      case 'disconnecting':
        return ConnectionStatus.disconnecting;
      case 'error':
        return ConnectionStatus.error;
      default:
        return ConnectionStatus.disconnected;
    }
  }

  /// getLastLoginResponse
  ///
  /// DESCRIPTION:
  ///     获取最后一次登录响应，用于提取best_server信息
  ///
  /// RETURNS:
  ///     Map<String, dynamic>? - 最后一次登录响应，如果没有则返回null
  Map<String, dynamic>? getLastLoginResponse() {
    return _lastLoginResponse;
  }
}
