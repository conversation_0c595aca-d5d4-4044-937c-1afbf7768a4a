/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// ******************************************************************************
/// FILE NAME :      main_screen.dart
///
/// DESCRIPTION :    Main application screen with UI and navigation system
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:window_manager/window_manager.dart';
import '../core/app_state.dart';
import '../core/dependency_injection.dart';
import '../services/connection_manager.dart';
import '../services/data_manager.dart';
import '../services/backend_service.dart';
import '../services/api_service.dart';
import '../services/log_service.dart';
import '../services/platform/platform_service_factory.dart';


import '../widgets/custom_window_frame.dart';
import '../widgets/adaptive_sidebar.dart';
import '../widgets/responsive_layout.dart';
import '../widgets/safe_area_wrapper.dart';
import '../widgets/android_gesture_wrapper.dart';

import '../utils/design_system.dart';
import '../models/connection_status.dart';

// 导入屏幕
import 'connection_screen.dart';
import 'statistics_screen.dart';
import 'logs_screen.dart';
import 'settings_screen.dart';
import 'about_screen.dart';
import 'user_screen.dart';

/// MainScreen
///
/// PURPOSE:
///     Main application screen that provides navigation between different functional areas.
///     Implements the UI with sidebar navigation and content area.
///
/// FEATURES:
///     - Sidebar navigation with design system integration
///     - Page transition animations with slide effects
///     - User editing state management for navigation control
///     - Connection status monitoring and management
///     - Graceful application shutdown with resource cleanup
///     - Custom window frame integration
///     - Background gradient styling
///     - Service lifecycle management
///
/// USAGE:
///     Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => MainScreen()))
class MainScreen extends StatefulWidget {
  /// MainScreen constructor
  ///
  /// DESCRIPTION:
  ///     Creates the main application screen widget.
  const MainScreen({Key? key}) : super(key: key);

  @override
  State<MainScreen> createState() => _MainScreenState();
}

/// _MainScreenState
///
/// PURPOSE:
///     State management for MainScreen widget.
///     Handles navigation, animations, service lifecycle, and application shutdown.
///
/// FEATURES:
///     - Service initialization and lifecycle management
///     - Page transition animations with slide effects
///     - Navigation state management with editing state tracking
///     - Graceful application shutdown with resource cleanup
///     - Connection status monitoring and management
class _MainScreenState extends State<MainScreen>
    with TickerProviderStateMixin {
  /// Connection manager service instance
  late final ConnectionManager _connectionManager;

  /// Application state service instance
  late final AppState _appState;

  /// Currently selected navigation index
  int _selectedIndex = 0;

  /// Scaffold key for accessing scaffold state
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  /// Whether user is currently editing profile information
  bool _isUserEditing = false;

  /// Animation controller for page transitions
  late AnimationController _pageTransitionController;

  /// Slide animation for page transitions
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _initializeData();
  }

  /// 初始化服务
  void _initializeServices() {
    _connectionManager = serviceLocator<ConnectionManager>();
    _appState = serviceLocator<AppState>();
  }

  /// 初始化动画
  void _initializeAnimations() {
    _pageTransitionController = AnimationController(
      duration: DesignSystem.animationMedium,
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.1, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _pageTransitionController,
      curve: DesignSystem.curveEmphasized,
    ));

    _pageTransitionController.forward();
  }

  /// 初始化数据
  Future<void> _initializeData() async {
    final dataManager = serviceLocator<DataManager>();
    await dataManager.initializeData();
  }

  @override
  void dispose() {
    _pageTransitionController.dispose();
    _connectionManager.dispose();
    // DataManager 的生命周期由依赖注入容器管理，不需要在这里手动释放
    super.dispose();
  }

  /// build
  ///
  /// DESCRIPTION:
  ///     Builds the main screen widget with sidebar navigation and content area.
  ///     Creates a responsive layout with custom window frame and background gradient.
  ///
  /// PARAMETERS:
  ///     context - Build context for the widget
  ///
  /// RETURNS:
  ///     Widget tree representing the main application screen
  @override
  Widget build(BuildContext context) {
    return AppWindowWrapper(
      child: ChangeNotifierProvider<AppState>.value(
        value: _appState,
        child: _buildPlatformSpecificLayout(),
      ),
    );
  }

  /// Build platform-specific layout
  Widget _buildPlatformSpecificLayout() {
    // Use desktop layout for desktop platforms and iOS App on macOS
    return FutureBuilder<bool>(
      future: PlatformServiceFactory.shouldUseDesktopLayout(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // While checking, use synchronous fallback
          final shouldUseDesktop = PlatformServiceFactory.shouldUseDesktopLayoutSync();
          return shouldUseDesktop ? _buildDesktopLayout() : _buildIOSLayout();
        }

        final shouldUseDesktop = snapshot.data ?? PlatformServiceFactory.shouldUseDesktopLayoutSync();
        return shouldUseDesktop ? _buildDesktopLayout() : _buildIOSLayout();
      },
    );
  }

  /// Update status bar style for iOS based on background color
  ///
  /// DESCRIPTION:
  ///     Updates the iOS status bar text color based on the current screen's background.
  ///     White backgrounds use dark status bar text, dark backgrounds use light status bar text.
  ///     Only applies to native iOS apps, not iOS apps running on macOS.
  ///
  /// PARAMETERS:
  ///     useWhiteBackground - Whether the current screen uses a white background
  ///
  /// RETURNS:
  ///     void
  void _updateStatusBarStyle(bool useWhiteBackground) async {
    // Only update status bar style for native iOS apps, not iOS apps on macOS
    if (Platform.isIOS && !(await PlatformServiceFactory.isIOSAppOnMacOS())) {
      final style = useWhiteBackground
          ? SystemUiOverlayStyle.dark // 白色背景使用深色状态栏文字（时间、信号等）
          : SystemUiOverlayStyle.light; // 深色背景使用浅色状态栏文字

      SystemChrome.setSystemUIOverlayStyle(style);

      // 调试信息
      try {
        // final logService = serviceLocator<LogService>();
        // logService.debug('MainScreen', 'Updated status bar style to ${useWhiteBackground ? "dark" : "light"} for screen index $_selectedIndex');
      } catch (e) {
        // 忽略日志错误，不影响功能
      }
    }
  }

  /// Build iOS-specific layout with drawer navigation
  Widget _buildIOSLayout() {
    // Determine if current screen should have white background for safe area
    final bool useWhiteBackground = _selectedIndex >= 2 && _selectedIndex <= 5;

    // 所有界面都使用深色状态栏内容（黑色状态栏）
    _updateStatusBarStyle(true);

    final scaffoldContent = Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.transparent,
      drawer: Drawer(
        child: AdaptiveSidebar.buildDrawerContent(
          context: context,
          selectedIndex: _selectedIndex,
          onItemSelected: (index) {
            Navigator.of(context).pop(); // Close drawer
            _onItemSelected(index);
          },
        ),
      ),
      body: Stack(
        children: [
          // Main content with status bar padding and specific screen padding
          Padding(
            padding: EdgeInsets.only(
              top: (_selectedIndex == 4) // 日志界面有AppBar，不需要状态栏间距
                  ? 0
                  : MediaQuery.of(context).padding.top + // 状态栏高度
                      ((_selectedIndex == 2 || _selectedIndex == 3 || _selectedIndex == 5) ? 30 : 0), // 统计、设置、关于界面额外30px间距
            ),
            child: _buildMainContent(),
          ),
          // Floating menu button just below status bar
          Positioned(
            top: MediaQuery.of(context).padding.top + 4, // 状态栏下方4px
            left: 16,
            child: Builder(
              builder: (context) => FloatingActionButton(
                mini: true,
                backgroundColor: AppColors.primary.withValues(alpha: 0.9),
                onPressed: () => Scaffold.of(context).openDrawer(),
                child: const Icon(
                  Icons.menu,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );

    // 使用Android手势包装器包装Scaffold内容，提供手势支持
    final gestureWrappedContent = AndroidGestureBuilder.wrap(
      scaffoldKey: _scaffoldKey,
      onDrawerToggle: () {
        // 侧边栏切换时的回调处理
        // 可以在这里添加额外的逻辑，如动画或状态更新
      },
      child: scaffoldContent,
    );

    return SafeAreaWrapper(
      backgroundColor: useWhiteBackground ? AppColors.backgroundStart : null,
      child: gestureWrappedContent,
    );
  }

  /// Build desktop layout (macOS/Windows) with existing behavior
  Widget _buildDesktopLayout() {
    final scaffoldContent = Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.transparent,
      body: ResponsiveLayout(
        sidebar: _buildAdaptiveSidebar(),
        content: _buildMainContent(),
      ),
    );

    // Only use CustomWindowFrame on Windows/Linux, not on macOS (including iOS apps on macOS)
    if (Platform.isWindows || Platform.isLinux) {
      return CustomWindowFrame(child: scaffoldContent);
    } else {
      return scaffoldContent;
    }
  }

  /// _buildAdaptiveSidebar
  ///
  /// DESCRIPTION:
  ///     Builds the adaptive sidebar navigation widget with platform-specific behavior.
  ///     Uses AdaptiveSidebar for iOS collapsible functionality and desktop compatibility.
  ///
  /// RETURNS:
  ///     Widget representing the adaptive sidebar navigation
  Widget _buildAdaptiveSidebar() {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return AdaptiveSidebar(
          selectedIndex: _selectedIndex,
          onItemSelected: _onItemSelected,
          userInfo: appState.userInfo,
          onEditProfile: null, // 在用户屏幕中处理编辑功能
          onLogout: _handleLogout,
          enableTabNavigation: !_isUserEditing, // 当用户在编辑时禁用侧边栏Tab导航
        );
      },
    );
  }

  /// _buildMainContent
  ///
  /// DESCRIPTION:
  ///     Builds the main content area with platform-specific styling and animations.
  ///     Wraps content with appropriate decorations and transitions.
  ///     Uses white background for certain screens (statistics, settings, logs, about).
  ///
  /// RETURNS:
  ///     Widget representing the styled main content area
  Widget _buildMainContent() {
    // Determine if current screen should have white background
    final bool useWhiteBackground = _selectedIndex >= 2 && _selectedIndex <= 5;

    return Container(
      decoration: useWhiteBackground
          ? const BoxDecoration(color: Colors.white)
          : const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: AppColors.backgroundGradient,
              ),
            ),
      child: SlideTransition(
        position: _slideAnimation,
        child: _buildContent(),
      ),
    );
  }

  /// _buildContent
  ///
  /// DESCRIPTION:
  ///     Builds the main content area based on selected navigation index.
  ///     Returns appropriate screen widget for current navigation state.
  ///
  /// RETURNS:
  ///     Widget representing the current screen content
  Widget _buildContent() {
    switch (_selectedIndex) {
      case 0:
        return const ConnectionScreen(); // 连接屏幕
      case 1:
        return UserScreen(
          onEditingStateChanged: (isEditing) {
            setState(() {
              _isUserEditing = isEditing;
            });
          },
        ); // 用户屏幕
      case 2:
        return const StatisticsScreen(); // 统计屏幕
      case 3:
        return const SettingsScreen(); // 设置屏幕
      case 4:
        return const LogsScreen(); // 日志屏幕
      case 5:
        return const AboutScreen(); // 关于屏幕
      default:
        return const ConnectionScreen();
    }
  }

  /// _onItemSelected
  ///
  /// DESCRIPTION:
  ///     Handles navigation item selection and updates the current screen.
  ///     Manages editing state and triggers page transition animations.
  ///     Updates status bar style for iOS when switching between screens.
  ///
  /// PARAMETERS:
  ///     index - Selected navigation index
  void _onItemSelected(int index) {
    setState(() {
      _selectedIndex = index;
      // 如果切换到非用户屏幕，重置编辑状态
      if (index != 1) {
        _isUserEditing = false;
      }
    });

    // Update status bar style for iOS when switching screens
    if (Platform.isIOS) {
      // 所有界面都使用深色状态栏内容（黑色状态栏）
      _updateStatusBarStyle(true);
    }

    // 触发页面过渡动画
    _pageTransitionController.reset();
    _pageTransitionController.forward();
  }

  /// _handleLogout
  ///
  /// DESCRIPTION:
  ///     Handles application logout and graceful shutdown process.
  ///     Disconnects WAN connection, stops backend service, and closes application.
  ///     Ensures proper resource cleanup before application termination.
  Future<void> _handleLogout() async {
    final logService = serviceLocator<LogService>();
    logService.info('MainScreen', '=== Starting sidebar exit process ===');
    logService.info('MainScreen', 'User selected exit application from sidebar');

    try {
      // 获取服务实例
      final apiService = serviceLocator<ApiService>();
      final backendService = serviceLocator<BackendService>();
      final appState = serviceLocator<AppState>();

      logService.info('MainScreen', 'Current connection status: ${appState.connectionStatus}');

      // 1. 如果已连接，先断开连接
      if (appState.connectionStatus == ConnectionStatus.connected) {
        logService.info('MainScreen', 'WAN connected detected, starting disconnection...');

        // 更新状态为断开连接中
        appState.updateConnectionStatus(
          ConnectionStatus.disconnecting,
          message: 'Disconnecting...', // Fallback message for logout process
        );

        try {
          // 断开WAN连接
          logService.info('MainScreen', 'Calling API to disconnect WAN...');
          await apiService.disconnect();
          logService.info('MainScreen', 'WAN disconnection successful');
          // 等待一下，确保断开连接完成
          await Future.delayed(const Duration(seconds: 1));
        } catch (e) {
          logService.error('MainScreen', 'WAN disconnection failed', e);
        }
      } else {
        logService.info('MainScreen', 'WAN not connected, skipping disconnection step');
      }

      // 2. 停止后端服务
      logService.info('MainScreen', 'Starting to stop backend service...');

      // 尝试通过API关闭后端服务
      bool backendShutdownSuccess = false;
      try {
        logService.info('MainScreen', 'Requesting backend service shutdown via API...');
        final result = await apiService.shutdownBackend();
        logService.info('MainScreen', 'Backend service shutdown request result: $result');
        // 给后端一些时间来处理关闭请求
        await Future.delayed(const Duration(seconds: 1));
        backendShutdownSuccess = true;
      } catch (e) {
        // 忽略错误，因为后端服务可能已经关闭
        logService.info('MainScreen', 'Backend service shutdown via API failed: $e');
      }

      // 如果通过API关闭失败，尝试通过BackendService停止后端
      if (!backendShutdownSuccess) {
        try {
          logService.info('MainScreen', 'Stopping backend service via BackendService...');
          await backendService.stopBackend();
          logService.info('MainScreen', 'Backend service stopped via BackendService successfully');
          backendShutdownSuccess = true;
        } catch (e) {
          logService.error('MainScreen', 'Failed to stop backend service via BackendService', e);
        }
      }

      logService.info('MainScreen', 'Backend service shutdown ${backendShutdownSuccess ? 'successful' : 'may have failed'}');

      // 3. 确保所有资源释放后再关闭窗口
      logService.info('MainScreen', 'Preparing to close window...');

      // 先显示窗口，然后再关闭，这样可以确保窗口被正确关闭
      try {
        await windowManager.show();
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        // 忽略窗口显示错误
      }

      try {
        // 强制关闭窗口
        logService.info('MainScreen', 'Setting preventClose to false');
        await windowManager.setPreventClose(false);
        logService.info('MainScreen', 'Calling windowManager.destroy()');
        await windowManager.destroy();
        logService.info('MainScreen', 'Window destruction completed');
      } catch (e) {
        logService.error('MainScreen', 'Failed to destroy window', e);
      }

      // 等待一下，确保所有操作完成
      await Future.delayed(const Duration(seconds: 1));

      // 4. 无论如何，强制退出应用
      logService.info('MainScreen', 'Force exiting application');
      exit(0);
    } catch (e) {
      logService.error('MainScreen', 'Error occurred during complete exit process', e);

      // 如果完整退出失败，强制退出
      logService.info('MainScreen', 'Exit process exception, force exiting application');
      exit(1);
    }
  }
}
