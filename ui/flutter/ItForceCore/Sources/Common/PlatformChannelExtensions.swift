/**
 * FILE: PlatformChannelExtensions.swift
 *
 * DESCRIPTION:
 *     Platform Channel data conversion extensions for ItForceCore types.
 *     Provides toDictionary methods matching Flutter frontend data models exactly.
 *     Ensures perfect compatibility with Flutter Server, TrafficStats, and ConnectionStatus models.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create Platform Channel data conversion extensions matching Flutter models
 */

import Foundation

// MARK: - ServerInfo Extensions

extension ServerInfo {

    /**
     * NAME: toDictionary
     *
     * DESCRIPTION:
     *     Converts ServerInfo to dictionary matching Flutter Server.fromJson() exactly.
     *     Field names and types must match Flutter lib/models/server.dart expectations.
     *
     * RETURNS:
     *     [String: Any] - Dictionary compatible with Flutter Server model
     */
    public func toDictionary() -> [String: Any] {
        [
            "id": id,
            "name": name,
            "name_en": nameEn,
            "server_name": serverName,
            "server_port": serverPort,
            "ping": ping,
            "isauto": isAuto,
            "status": status.rawValue,
            "isdefault": false
        ]
    }
}

// MARK: - ConnectionState Extensions

extension ConnectionState {

    /**
     * NAME: flutterStatusString
     *
     * DESCRIPTION:
     *     Maps Swift ConnectionState to Flutter ConnectionStatus enum values exactly.
     *     Must match Flutter lib/utils/constants.dart ConnectionStatusStrings values.
     *
     *     Flutter ConnectionStatus enum:
     *     - disconnected, connecting, connected, disconnecting, error
     *
     * RETURNS:
     *     String - Status string compatible with Flutter ConnectionStatus
     */
    public var flutterStatusString: String {
        switch self {
        case .initializing, .serverResolved, .authenticating:
            return "connecting"  // Maps to ConnectionStatus.connecting
        case .connected:
            return "connected"   // Maps to ConnectionStatus.connected
        case .disconnected:
            return "disconnected" // Maps to ConnectionStatus.disconnected
        case .authenticationFailed:
            return "error"       // Maps to ConnectionStatus.error
        }
    }

    /**
     * NAME: flutterStatusString(isDisconnecting:)
     *
     * DESCRIPTION:
     *     Maps Swift ConnectionState to Flutter ConnectionStatus with disconnecting support.
     *     Provides disconnecting state for UI transitions when Go backend doesn't have this state.
     *
     * PARAMETERS:
     *     isDisconnecting - Whether disconnection is in progress
     *
     * RETURNS:
     *     String - Status string compatible with Flutter ConnectionStatus
     */
    public func flutterStatusString(isDisconnecting: Bool = false) -> String {
        // Handle disconnecting state for Flutter UI compatibility
        if isDisconnecting && (self == .connected || self == .disconnected) {
            return "disconnecting" // Maps to ConnectionStatus.disconnecting
        }

        return flutterStatusString
    }
}

// MARK: - VPNConnectionInfo Extensions

extension VPNConnectionInfo {

    /**
     * NAME: toDictionary
     *
     * DESCRIPTION:
     *     Converts VPNConnectionInfo to dictionary for Platform Channel communication.
     *     Uses Swift's computed properties and optional chaining for clean code.
     *
     * RETURNS:
     *     [String: Any] - Dictionary representation
     */
    public func toDictionary() -> [String: Any] {
        var dict: [String: Any] = [
            "status": state.flutterStatusString,
            "message": state.flutterStatusString
        ]

        // Only include traffic_stats when connected (avoid sending 0 values for disconnected states)
        if state == .connected {
            dict["traffic_stats"] = trafficStats.toDictionary()
        }

        // Only include connected_time if we have a valid connection time
        if let connectionTime = connectionTime {
            dict["connected_time"] = Int64(connectionTime.timeIntervalSince1970)
        }

        // Use Swift's optional chaining and nil-coalescing
        if let server = connectedServer {
            dict["server"] = server.toDictionary()
        }

        if let error = lastError {
            dict["error"] = error
        }

        return dict
    }

    /**
     * NAME: toDictionary(includeNetworkInterface:vpnService:)
     *
     * DESCRIPTION:
     *     Converts VPNConnectionInfo to dictionary with optional network interface information.
     *     When includeNetworkInterface is true and state is connected, includes real-time
     *     network interface data for iOS platform integration.
     *
     * PARAMETERS:
     *     includeNetworkInterface - Whether to include network interface information
     *     vpnService - VPN service instance for getting tunnel IP (optional)
     *
     * RETURNS:
     *     [String: Any] - Dictionary representation with optional network interface data
     */
    public func toDictionary(includeNetworkInterface: Bool = false, vpnService: VPNService? = nil) -> [String: Any] {
        var dict: [String: Any] = [
            "status": state.flutterStatusString,
            "message": state.flutterStatusString
        ]

        // Only include traffic_stats when connected (avoid sending 0 values for disconnected states)
        if state == .connected {
            dict["traffic_stats"] = trafficStats.toDictionary()
        }

        // Only include connected_time if we have a valid connection time
        if let connectionTime = connectionTime {
            dict["connected_time"] = Int64(connectionTime.timeIntervalSince1970)
        }

        // Use Swift's optional chaining and nil-coalescing
        if let server = connectedServer {
            dict["server"] = server.toDictionary()
        }

        if let error = lastError {
            dict["error"] = error
        }

        // Include network interface information for iOS platform when connected
        if includeNetworkInterface && state == .connected {
            // print("Including network interface information in status event") // Debug log commented for production

            // Get real physical interface information
            let physicalInterface = NetworkInterfaceService.getPhysicalInterfaceInfo()

            // Get real VPN tunnel IP from App Group
            let tunnelIP = vpnService?.getTunnelIPAddress() ?? ""

            // print("Network interface data - interface: \(physicalInterface.interfaceName), local_ip: \(physicalInterface.localIP), itforce_ip: \(tunnelIP)") // Debug log commented for production

            // Add network interface fields to the status event
            dict["interface_name"] = physicalInterface.interfaceName
            dict["local_ip"] = physicalInterface.localIP
            dict["itforce_ip"] = tunnelIP

            // Log the inclusion for debugging
            // print("Network interface information added to status event") // Debug log commented for production
        }

        return dict
    }

    /**
     * NAME: toDictionary(isDisconnecting:)
     *
     * DESCRIPTION:
     *     Converts VPNConnectionInfo to dictionary with disconnecting state support.
     *     Provides Flutter-compatible disconnecting status for UI transitions.
     *
     * PARAMETERS:
     *     isDisconnecting - Whether disconnection is in progress
     *
     * RETURNS:
     *     [String: Any] - Dictionary representation with disconnecting support
     */
    public func toDictionary(isDisconnecting: Bool = false) -> [String: Any] {
        let statusString = state.flutterStatusString(isDisconnecting: isDisconnecting)

        var dict: [String: Any] = [
            "status": statusString,
            "message": statusString
        ]

        // Only include traffic_stats when connected (avoid sending 0 values for disconnected/disconnecting states)
        if state == .connected && !isDisconnecting {
            dict["traffic_stats"] = trafficStats.toDictionary()
        }

        // Only include connected_time if we have a valid connection time
        if let connectionTime = connectionTime {
            dict["connected_time"] = Int64(connectionTime.timeIntervalSince1970)
        }

        // Use Swift's optional chaining and nil-coalescing
        if let server = connectedServer {
            dict["server"] = server.toDictionary()
        }

        if let error = lastError {
            dict["error"] = error
        }

        return dict
    }
}

// MARK: - TrafficStatistics Extensions

extension TrafficStatistics {

    /**
     * NAME: toDictionary
     *
     * DESCRIPTION:
     *     Converts TrafficStatistics to dictionary matching Flutter TrafficStats.fromJson() exactly.
     *     Field names and timestamp format must match Flutter lib/models/traffic_stats.dart expectations.
     *
     * RETURNS:
     *     [String: Any] - Dictionary compatible with Flutter TrafficStats model
     */
    public func toDictionary() -> [String: Any] {
        [
            "upload_speed": uploadSpeed,
            "download_speed": downloadSpeed,
            "total_upload": totalUpload,
            "total_download": totalDownload,
            "timestamp": Int64(lastUpdate.timeIntervalSince1970 * 1000) // Flutter expects milliseconds
        ]
    }
}

// MARK: - VPNServiceError Extensions

extension VPNServiceError {

    /**
     * NAME: toDictionary
     *
     * DESCRIPTION:
     *     Converts VPNServiceError to dictionary for Flutter error handling.
     *     Uses Swift's pattern matching for clean error categorization.
     *
     * RETURNS:
     *     [String: Any] - Dictionary compatible with Flutter error handling
     */
    public func toDictionary() -> [String: Any] {
        [
            "code": errorCode,
            "message": localizedDescription,
            "type": errorType
        ]
    }

    /**
     * NAME: errorCode
     *
     * DESCRIPTION:
     *     Gets numeric error code using Swift's computed property.
     */
    public var errorCode: Int {
        switch self {
        case .serviceNotStarted: return 1001
        case .serviceAlreadyStarted: return 1002
        case .connectionManagerError: return 1003
        case .serverServiceError: return 1004
        case .configurationInvalid: return 1005
        case .vpnPermissionDenied: return 1006
        case .networkExtensionError: return 1007
        case .operationInProgress: return 1008
        case .noCredentialsConfigured: return 1009
        case .connectionInProgress: return 1010
        case .disconnectionInProgress: return 1011
        case .networkExtensionNotAvailable: return 1012
        case .serverNotSelected: return 1013
        case .authenticationFailed: return 1014
        case .authenticationTimeout: return 1015
        case .connectionTimeout: return 1016
        case .platformChannelError: return 1017
        case .autoReconnectFailed: return 1018
        }
    }

    /**
     * NAME: errorType
     *
     * DESCRIPTION:
     *     Gets error type string using Swift's pattern matching.
     */
    public var errorType: String {
        switch self {
        case .serviceNotStarted: return "service_not_started"
        case .serviceAlreadyStarted: return "service_already_started"
        case .connectionManagerError: return "connection_manager_error"
        case .serverServiceError: return "server_service_error"
        case .configurationInvalid: return "configuration_invalid"
        case .vpnPermissionDenied: return "vpn_permission_denied"
        case .networkExtensionError: return "network_extension_error"
        case .operationInProgress: return "operation_in_progress"
        case .noCredentialsConfigured: return "no_credentials_configured"
        case .connectionInProgress: return "connection_in_progress"
        case .disconnectionInProgress: return "disconnection_in_progress"
        case .networkExtensionNotAvailable: return "network_extension_not_available"
        case .serverNotSelected: return "server_not_selected"
        case .authenticationFailed: return "authentication_failed"
        case .authenticationTimeout: return "authentication_timeout"
        case .connectionTimeout: return "connection_timeout"
        case .platformChannelError: return "platform_channel_error"
        case .autoReconnectFailed: return "auto_reconnect_failed"
        }
    }
}

// MARK: - InterfaceInfo Structure

/**
 * NAME: InterfaceInfo
 *
 * DESCRIPTION:
 *     Network interface information for Platform Channel communication.
 *     Simple struct using Swift's memberwise initializer.
 */
public struct InterfaceInfo {
    public let localIP: String
    public let tunIP: String
    public let gateway: String
    public let dns: [String]
    public let mtu: Int

    public init(
        localIP: String = "",
        tunIP: String = "",
        gateway: String = "",
        dns: [String] = [],
        mtu: Int = 1400
    ) {
        self.localIP = localIP
        self.tunIP = tunIP
        self.gateway = gateway
        self.dns = dns
        self.mtu = mtu
    }

    /**
     * NAME: toDictionary
     *
     * DESCRIPTION:
     *     Converts to dictionary using Swift's concise syntax.
     */
    public func toDictionary() -> [String: Any] {
        [
            "local_ip": localIP,
            "tun_ip": tunIP,
            "gateway": gateway,
            "dns": dns,
            "mtu": mtu
        ]
    }
}
