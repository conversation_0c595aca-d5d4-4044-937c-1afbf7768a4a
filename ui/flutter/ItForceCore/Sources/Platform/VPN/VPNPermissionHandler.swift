/**
 * FILE: VPNPermissionHandler.swift
 *
 * DESCRIPTION:
 *     VPN permission handler providing user-friendly VPN authorization flow.
 *     Handles permission requests, user guidance, and error messaging.
 *     Provides comprehensive VPN permission management for iOS/macOS.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import NetworkExtension
import <PERSON>Log

/**
 * NAME: VPNPermissionStatus
 *
 * DESCRIPTION:
 *     VPN permission status enumeration.
 */
public enum VPNPermissionStatus: String, CaseIterable {
    case notDetermined = "not_determined"
    case denied = "denied"
    case authorized = "authorized"
    case restricted = "restricted"
}

/**
 * NAME: RoutingMode
 *
 * DESCRIPTION:
 *     Enumeration for VPN routing modes.
 *
 * CASES:
 *     all - Route all traffic through VPN
 *     custom - Route only specified networks through VPN
 */
public enum RoutingMode: String, CaseIterable {
    case all = "all"
    case custom = "custom"
}

/**
 * NAME: VPNConfiguration
 *
 * DESCRIPTION:
 *     VPN configuration structure for permission requests.
 *     Enhanced with routing configuration support.
 */
public struct VPNConfiguration {
    public let serverAddress: String
    public let serverPort: Int
    public let username: String
    public let password: String
    public let mtu: Int
    public let encryptionMethod: UInt8
    public let dnsServers: [String]
    public let excludedIPs: [String]
    public let tunnelIP: String
    public let routingMode: RoutingMode
    public let customRoutes: [String]

    public init(
        serverAddress: String,
        serverPort: Int,
        username: String,
        password: String,
        mtu: Int = 1400,
        encryptionMethod: UInt8 = 1,
        dnsServers: [String] = ["*******", "*******"],
        excludedIPs: [String] = [],
        tunnelIP: String = "********",
        routingMode: RoutingMode = .all,
        customRoutes: [String] = []
    ) {
        self.serverAddress = serverAddress
        self.serverPort = serverPort
        self.username = username
        self.password = password
        self.mtu = mtu
        self.encryptionMethod = encryptionMethod
        self.dnsServers = dnsServers
        self.excludedIPs = excludedIPs
        self.tunnelIP = tunnelIP
        self.routingMode = routingMode
        self.customRoutes = customRoutes
    }
}

/**
 * NAME: VPNPermissionResult
 *
 * DESCRIPTION:
 *     Result of VPN permission request with detailed information.
 */
public struct VPNPermissionResult {
    public let granted: Bool
    public let status: VPNPermissionStatus
    public let error: VPNServiceError?
    public let userMessage: String

    public init(granted: Bool, status: VPNPermissionStatus, error: VPNServiceError? = nil, userMessage: String) {
        self.granted = granted
        self.status = status
        self.error = error
        self.userMessage = userMessage
    }
}

/**
 * NAME: VPNPermissionHandlerDelegate
 *
 * DESCRIPTION:
 *     Delegate protocol for VPN permission handler events.
 */
public protocol VPNPermissionHandlerDelegate: AnyObject {
    func permissionHandler(_ handler: VPNPermissionHandler, didRequestPermission result: VPNPermissionResult)
    func permissionHandler(_ handler: VPNPermissionHandler, needsUserGuidance message: String)
}

/**
 * NAME: VPNPermissionHandler
 *
 * DESCRIPTION:
 *     Comprehensive VPN permission handler with user-friendly flow.
 *     Manages VPN authorization requests, provides user guidance, and handles errors gracefully.
 *     Uses VPNService NetworkExtension functionality directly.
 *
 * PROPERTIES:
 *     vpnService - VPN service instance
 *     delegate - Delegate for permission events
 *     logger - Logger instance
 */
public class VPNPermissionHandler {
    // MARK: - Properties

    private weak var vpnService: VPNService?
    public weak var delegate: VPNPermissionHandlerDelegate?
    private let logger: LoggerProtocol

    // MARK: - Initialization

    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Initializes VPN permission handler with VPN service.
     *
     * PARAMETERS:
     *     vpnService - VPN service instance
     *     logger - Logger instance
     */
    public init(vpnService: VPNService, logger: LoggerProtocol) {
        self.vpnService = vpnService
        self.logger = logger
    }
    
    // MARK: - Public Interface
    
    /**
     * NAME: handleVPNPermissionFlow
     *
     * DESCRIPTION:
     *     Handles complete VPN permission flow with user guidance.
     *     Checks current status and guides user through authorization process.
     *
     * PARAMETERS:
     *     configuration - VPN configuration for permission request
     *
     * RETURNS:
     *     VPNPermissionResult - Result of permission flow
     */
    public func handleVPNPermissionFlow(with configuration: VPNConfiguration) async -> VPNPermissionResult {
        guard vpnService != nil else {
            return VPNPermissionResult(
                granted: false,
                status: .denied,
                error: .configurationInvalid("VPN service not available"),
                userMessage: "VPN服务不可用"
            )
        }

        logger.info("Starting VPN permission flow")

        // Check current permission status
        let currentStatus = await checkPermissionStatus()

        switch currentStatus {
        case .notDetermined:
            return await requestVPNPermissionWithGuidance(configuration: configuration)

        case .denied:
            return handlePermissionDenied()

        case .restricted:
            return handleSystemRestricted()

        case .authorized:
            return VPNPermissionResult(
                granted: true,
                status: .authorized,
                userMessage: "VPN权限已授权，可以建立连接"
            )
        }
    }
    
    /**
     * NAME: checkVPNPermissionStatus
     *
     * DESCRIPTION:
     *     Checks VPN permission status with user-friendly messages.
     *
     * RETURNS:
     *     VPNPermissionResult - Current permission status with message
     */
    public func checkVPNPermissionStatus() async -> VPNPermissionResult {
        let status = await checkPermissionStatus()

        switch status {
        case .notDetermined:
            return VPNPermissionResult(
                granted: false,
                status: .notDetermined,
                userMessage: "需要请求VPN权限才能建立连接"
            )

        case .denied:
            return VPNPermissionResult(
                granted: false,
                status: .denied,
                userMessage: "VPN权限被拒绝，请在设置中手动启用"
            )

        case .restricted:
            return VPNPermissionResult(
                granted: false,
                status: .restricted,
                userMessage: "VPN功能被系统限制，请检查家长控制或企业策略设置"
            )

        case .authorized:
            return VPNPermissionResult(
                granted: true,
                status: .authorized,
                userMessage: "VPN权限已授权"
            )
        }
    }

    /**
     * NAME: checkPermissionStatus
     *
     * DESCRIPTION:
     *     Checks current VPN permission status using VPNService.
     *
     * RETURNS:
     *     VPNPermissionStatus - Current permission status
     */
    private func checkPermissionStatus() async -> VPNPermissionStatus {
        guard let vpnService = vpnService else {
            return .denied
        }

        return await vpnService.checkNetworkExtensionPermissionStatus()
    }
    
    /**
     * NAME: requestVPNPermissionWithRetry
     *
     * DESCRIPTION:
     *     Requests VPN permission with retry logic and user guidance.
     *
     * PARAMETERS:
     *     configuration - VPN configuration
     *     maxRetries - Maximum retry attempts
     *
     * RETURNS:
     *     VPNPermissionResult - Permission request result
     */
    public func requestVPNPermissionWithRetry(
        configuration: VPNConfiguration,
        maxRetries: Int = 2
    ) async -> VPNPermissionResult {
        var attempts = 0
        
        while attempts < maxRetries {
            attempts += 1
            
            logger.info("VPN permission request attempt", metadata: [
                "attempt": "\(attempts)",
                "max_retries": "\(maxRetries)"
            ])
            
            let result = await requestVPNPermissionWithGuidance(configuration: configuration)
            
            if result.granted {
                return result
            }
            
            // If not the last attempt, wait before retrying
            if attempts < maxRetries {
                logger.info("Waiting before retry", metadata: ["attempt": "\(attempts)"])
                try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            }
        }
        
        // All attempts failed
        return VPNPermissionResult(
            granted: false,
            status: .denied,
            error: .vpnPermissionDenied,
            userMessage: "多次尝试后VPN权限仍被拒绝，请在系统设置中手动启用VPN配置"
        )
    }
    
    // MARK: - Private Implementation

    /**
     * NAME: convertToVPNServiceConfiguration
     *
     * DESCRIPTION:
     *     Converts VPNConfiguration to VPNService's VPNConfiguration format.
     */
    private func convertToVPNServiceConfiguration(_ config: VPNConfiguration) -> VPNConfiguration {
        // For now, just return the same configuration since they have the same structure
        return config
    }
    
    /**
     * NAME: requestVPNPermissionWithGuidance
     *
     * DESCRIPTION:
     *     Requests VPN permission with user guidance messages.
     *
     * PARAMETERS:
     *     configuration - VPN configuration
     *
     * RETURNS:
     *     VPNPermissionResult - Permission request result
     */
    private func requestVPNPermissionWithGuidance(configuration: VPNConfiguration) async -> VPNPermissionResult {
        // Notify delegate about permission request
        delegate?.permissionHandler(self, needsUserGuidance: "VPN permission will be requested. Please select 'Allow' in the dialog.")
        
        guard let vpnService = vpnService else {
            return VPNPermissionResult(
                granted: false,
                status: .denied,
                error: .configurationInvalid("VPN service not available"),
                userMessage: "VPN服务不可用"
            )
        }

        do {
            // Convert VPNConfiguration to VPNService's VPNConfiguration format
            let vpnConfig = convertToVPNServiceConfiguration(configuration)
            let granted = try await vpnService.requestNetworkExtensionPermission(with: vpnConfig)
            
            if granted {
                logger.info("VPN permission granted successfully")
                return VPNPermissionResult(
                    granted: true,
                    status: .authorized,
                    userMessage: "VPN权限已成功授权"
                )
            } else {
                logger.warning("VPN permission was denied by user")
                return VPNPermissionResult(
                    granted: false,
                    status: .denied,
                    error: .vpnPermissionDenied,
                    userMessage: "VPN权限被拒绝，无法建立VPN连接"
                )
            }
            
        } catch let error as VPNServiceError {
            logger.error("VPN permission request failed", metadata: [
                "error": error.localizedDescription
            ])

            return VPNPermissionResult(
                granted: false,
                status: .denied,
                error: error,
                userMessage: getErrorMessage(for: error)
            )

        } catch {
            logger.error("Unexpected error during VPN permission request", metadata: [
                "error": error.localizedDescription
            ])

            let vpnError = VPNServiceError.configurationInvalid(error.localizedDescription)
            return VPNPermissionResult(
                granted: false,
                status: .denied,
                error: vpnError,
                userMessage: "VPN权限请求失败：\(error.localizedDescription)"
            )
        }
    }
    
    /**
     * NAME: handlePermissionDenied
     *
     * DESCRIPTION:
     *     Handles case where VPN permission was previously denied.
     *
     * RETURNS:
     *     VPNPermissionResult - Permission denied result with guidance
     */
    private func handlePermissionDenied() -> VPNPermissionResult {
        let message = "VPN权限之前被拒绝。请前往 设置 > 通用 > VPN与设备管理 > VPN 启用ItForce VPN配置"
        
        delegate?.permissionHandler(self, needsUserGuidance: message)
        
        return VPNPermissionResult(
            granted: false,
            status: .denied,
            error: .vpnPermissionDenied,
            userMessage: message
        )
    }
    
    /**
     * NAME: handleSystemRestricted
     *
     * DESCRIPTION:
     *     Handles case where VPN is restricted by system policy.
     *
     * RETURNS:
     *     VPNPermissionResult - System restricted result with guidance
     */
    private func handleSystemRestricted() -> VPNPermissionResult {
        let message = "VPN功能被系统策略限制。请检查家长控制设置或联系系统管理员"
        
        delegate?.permissionHandler(self, needsUserGuidance: message)
        
        return VPNPermissionResult(
            granted: false,
            status: .restricted,
            error: .vpnPermissionDenied,
            userMessage: message
        )
    }
    
    /**
     * NAME: getErrorMessage
     *
     * DESCRIPTION:
     *     Gets user-friendly error message for VPN service errors.
     *
     * PARAMETERS:
     *     error - VPN service error
     *
     * RETURNS:
     *     String - User-friendly error message
     */
    private func getErrorMessage(for error: VPNServiceError) -> String {
        switch error {
        case .serviceNotStarted:
            return "VPN服务未启动"
        case .serviceAlreadyStarted:
            return "VPN服务已经启动"
        case .connectionManagerError(let connectionError):
            return "VPN连接失败：\(connectionError.localizedDescription)"
        case .serverServiceError(let serverError):
            return "服务器错误：\(serverError.localizedDescription)"
        case .configurationInvalid(let message):
            return "VPN配置失败：\(message)"
        case .vpnPermissionDenied:
            return "VPN权限被拒绝，请在系统设置中手动启用"
        case .networkExtensionError(let message):
            return "网络扩展错误：\(message)"
        case .operationInProgress:
            return "VPN操作正在进行中，请稍候"
        case .noCredentialsConfigured:
            return "未配置VPN凭据"
        case .connectionInProgress:
            return "VPN连接正在进行中"
        case .disconnectionInProgress:
            return "VPN断开连接正在进行中"
        case .networkExtensionNotAvailable:
            return "网络扩展不可用"
        case .serverNotSelected:
            return "未选择服务器"
        case .authenticationFailed:
            return "VPN认证失败，请检查凭据"
        case .authenticationTimeout:
            return "VPN认证超时"
        case .connectionTimeout:
            return "VPN连接超时"
        case .platformChannelError(let message):
            return "平台通道错误：\(message)"
        case .autoReconnectFailed(let message):
            return "自动重连失败：\(message)"
        }
    }
}
