/**
 * FILE: SDWANProtocol.kt
 *
 * DESCRIPTION:
 *     Main SDWAN protocol implementation for Android.
 *     Handles authentication, heartbeat, and data transmission.
 *     Compatible with Go backend SDWAN protocol implementation.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.protocol

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import com.panabit.client.protocol.models.*
import kotlinx.coroutines.*
import java.net.DatagramSocket
import java.net.DatagramPacket
import java.net.InetAddress

/**
 * NAME: SDWANProtocol
 *
 * DESCRIPTION:
 *     Main SDWAN protocol implementation class.
 *     Manages protocol state machine, authentication, and packet processing.
 *     Provides high-level interface for SDWAN protocol operations.
 *
 * PROPERTIES:
 *     stateMachine - Protocol state machine
 *     encryptionService - Encryption service for data packets
 *     keyManager - Key management for session and password keys
 *     tlvParser - TLV attribute parser
 *     socket - UDP socket for network communication
 *     serverAddress - Target server address (can be IP or hostname)
 *     serverPort - Target server port
 */
class SDWANProtocol(
    private val serverAddress: String,
    private val serverPort: Int
) {
    private val _stateMachine = ProtocolStateMachine()
    private var _encryptionService: EncryptionService = NoEncryption()
    private val keyManager = KeyManager()
    private val tlvParser = TLVParser()
    private var socket: DatagramSocket? = null
    // Heartbeat is now handled by ConnectionManager's HeartbeatManager - removed duplicate implementation
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // Server-assigned configuration
    @Volatile private var tunnelIP: String? = null
    @Volatile private var gatewayIP: String? = null
    @Volatile private var dnsIP: String? = null
    @Volatile private var mtuValue: Int? = null

    /**
     * NAME: stateMachine
     *
     * DESCRIPTION:
     *     Public getter for the protocol state machine.
     *     Required for HeartbeatManager integration.
     *
     * RETURNS:
     *     ProtocolStateMachine - The protocol state machine
     */
    val stateMachine: ProtocolStateMachine
        get() = _stateMachine

    /**
     * NAME: encryptionService
     *
     * DESCRIPTION:
     *     Public getter for the encryption service.
     *     Required for HeartbeatManager integration.
     *
     * RETURNS:
     *     EncryptionService - The encryption service
     */
    val encryptionService: EncryptionService
        get() = _encryptionService

    /**
     * NAME: getTunnelIP
     *
     * DESCRIPTION:
     *     Gets the server-assigned tunnel IP address.
     *     Available after successful authentication.
     *
     * RETURNS:
     *     String? - Tunnel IP address or null if not assigned
     */
    fun getTunnelIP(): String? = tunnelIP

    /**
     * NAME: getGatewayIP
     *
     * DESCRIPTION:
     *     Gets the server-assigned gateway IP address.
     *     Available after successful authentication.
     *
     * RETURNS:
     *     String? - Gateway IP address or null if not assigned
     */
    fun getGatewayIP(): String? = gatewayIP

    /**
     * NAME: getDnsIP
     *
     * DESCRIPTION:
     *     Gets the server-assigned DNS IP address.
     *     Available after successful authentication.
     *
     * RETURNS:
     *     String? - DNS IP address or null if not assigned
     */
    fun getDnsIP(): String? = dnsIP

    /**
     * NAME: getMtu
     *
     * DESCRIPTION:
     *     Gets the server-assigned MTU value.
     *     Available after successful authentication.
     *
     * RETURNS:
     *     Int? - MTU value or null if not assigned
     */
    fun getMtu(): Int? = mtuValue

    companion object {
        // Heartbeat constants moved to HeartbeatManager - removed duplicate definitions
        private const val AUTH_TIMEOUT_MS = 8000L        // 8 seconds total
        private const val AUTH_RETRY_TIMEOUT_MS = 2000L  // 2 seconds per attempt
        private const val MAX_AUTH_RETRIES = 3

        /**
         * NAME: AuthenticationResult
         *
         * DESCRIPTION:
         *     Data class representing authentication result.
         *     Contains success status, session information, error details, and network latency.
         *     Compatible with iOS AuthenticationResult.
         *
         * PROPERTIES:
         *     success - Whether authentication was successful
         *     sessionID - Session ID assigned by server (null if failed)
         *     token - Authentication token for subsequent requests (null if failed)
         *     errorMessage - Error message if authentication failed
         *     latencyMs - Round-trip time in milliseconds for authentication (null if failed)
         */
        data class AuthenticationResult(
            val success: Boolean,
            val sessionID: UShort? = null,
            val token: UInt? = null,
            val errorMessage: String? = null,
            val latencyMs: Int? = null
        )
    }

    /**
     * NAME: authenticateWithSocket
     *
     * DESCRIPTION:
     *     Performs SDWAN authentication using an external socket.
     *     Updates internal state machine on successful authentication.
     *     Sends OPEN packet and waits for OPENACK response with retry logic.
     *
     * PARAMETERS:
     *     socket - External UDP socket to use for authentication
     *     username - Username for authentication
     *     password - Password for authentication
     *     mtu - MTU value for connection
     *     encryptionMethod - Encryption method to use
     *     targetIP - Pre-resolved IP address (optional, defaults to serverAddress for backward compatibility)
     *
     * RETURNS:
     *     AuthenticationResult - Authentication result with session info or error
     *
     * THROWS:
     *     Exception - If authentication fails or times out
     */
    suspend fun authenticateWithSocket(
        socket: DatagramSocket,
        username: String,
        password: String,
        mtu: UShort = 1400u,
        encryptionMethod: EncryptionMethod = EncryptionMethod.XOR,
        targetIP: String? = null
    ): AuthenticationResult = withContext(Dispatchers.IO) {
        try {
            // logInfo("Starting SDWAN authentication with external socket", mapOf(
            //     "username" to username,
            //     "mtu" to mtu.toString(),
            //     "encryption" to encryptionMethod.name
            // ))

            // Initialize encryption service
            initializeEncryption(username, password, encryptionMethod)

            // Configure socket timeout for authentication
            val originalTimeout = socket.soTimeout
            socket.soTimeout = AUTH_RETRY_TIMEOUT_MS.toInt()

            try {
                // Build OPEN packet
                val openPacket = buildOpenPacket(username, password, mtu, encryptionMethod)

                // Perform authentication with retry logic
                var attempt = 0
                var lastError: String? = null
                var lastErrorWasTimeout = false
                var lastErrorWasReject = false

                while (attempt < MAX_AUTH_RETRIES) {
                    attempt++

                    // logDebug("Authentication attempt $attempt/$MAX_AUTH_RETRIES")

                    try {
                        // Record send timestamp for latency measurement
                        val sendTimestamp = System.currentTimeMillis()

                        // Send OPEN packet using external socket with resolved IP
                        sendPacketWithSocket(socket, openPacket, targetIP)

                        // Wait for OPENACK response
                        val response = withTimeout(AUTH_RETRY_TIMEOUT_MS) {
                            receiveAuthResponseWithSocket(socket)
                        }

                        // Record receive timestamp and calculate RTT
                        val receiveTimestamp = System.currentTimeMillis()
                        val rttMs = (receiveTimestamp - sendTimestamp).toInt()

                        if (response != null) {
                            // logDebug("Received response packet, processing...")
                            val result = processOpenAckPacketForAuth(response, rttMs)
                            // logDebug("Authentication result: success=${result.success}, error=${result.errorMessage}, latency=${rttMs}ms")
                            if (result.success) {
                                // 🔧 FIX: Update state machine on successful authentication
                                _stateMachine.setState(ProtocolState.DATA, "Authentication successful")

                                // Store session information in state machine
                                result.sessionID?.let { sessionID ->
                                    result.token?.let { token ->
                                        _stateMachine.setSessionInfo(sessionID, token)
                                    }
                                }

                                // logInfo("Authentication successful with state machine updated", mapOf(
                                //     "username" to username,
                                //     "session_id" to String.format("0x%04X", (result.sessionID ?: 0u).toInt()),
                                //     "token" to String.format("0x%08X", (result.token ?: 0u).toInt()),
                                //     "protocol_state" to _stateMachine.getState().description
                                // ))
                                return@withContext result
                            } else {
                                // 收到了响应但认证失败（OPENREJECT）
                                lastError = result.errorMessage
                                lastErrorWasReject = true
                                lastErrorWasTimeout = false
                                logError("Authentication rejected: ${result.errorMessage}")

                                // 如果是凭证错误（OPENREJECT），不需要重试
                                if (isCredentialRelatedError(result.errorMessage)) {
                                    logError("Credential error detected, stopping retries")
                                    break
                                }
                            }
                        } else {
                            // 没有收到响应
                            lastError = "No response received from server"
                            lastErrorWasTimeout = true
                            lastErrorWasReject = false
                            // logDebug("No response received for attempt $attempt")
                        }
                    } catch (e: TimeoutCancellationException) {
                        // 超时异常
                        lastError = "Authentication request timed out"
                        lastErrorWasTimeout = true
                        lastErrorWasReject = false
                        // logDebug("Authentication attempt $attempt timed out, retrying...")
                        continue
                    }
                }

                // 根据失败原因生成相应的错误消息
                val finalErrorMessage = when {
                    lastErrorWasReject && lastError != null -> {
                        // 收到了OPENREJECT响应
                        lastError
                    }
                    lastErrorWasTimeout -> {
                        // 所有重试都是超时
                        "Authentication timed out after $MAX_AUTH_RETRIES attempts - no response from server"
                    }
                    else -> {
                        // 其他未知错误
                        "Authentication failed after $MAX_AUTH_RETRIES attempts: ${lastError ?: "unknown error"}"
                    }
                }

                // Update state machine
                _stateMachine.setState(ProtocolState.AUTH_FAIL, finalErrorMessage)
                logError(finalErrorMessage)
                AuthenticationResult(
                    success = false,
                    errorMessage = finalErrorMessage
                )

            } finally {
                // Restore original socket timeout
                socket.soTimeout = originalTimeout
            }

        } catch (e: Exception) {
            // Update state machine on exception
            _stateMachine.setState(ProtocolState.AUTH_FAIL, "Authentication error: ${e.message}")
            logError("Authentication error", mapOf("username" to username), e)
            AuthenticationResult(
                success = false,
                errorMessage = "Authentication error: ${e.message}"
            )
        }
    }

    /**
     * NAME: authenticate
     *
     * DESCRIPTION:
     *     Performs SDWAN authentication with server.
     *     Sends OPEN packet and waits for OPENACK response.
     *     Implements retry logic with timeouts.
     *
     *     NOTE: This method maintains backward compatibility.
     *     For new code, prefer authenticateWithSocket() for better decoupling.
     *
     * PARAMETERS:
     *     username - Username for authentication
     *     password - Password for authentication
     *     mtu - MTU value for connection
     *     encryptionMethod - Encryption method to use
     *
     * RETURNS:
     *     Boolean - true if authentication successful, false otherwise
     *
     * THROWS:
     *     Exception - If authentication fails or times out
     */
    suspend fun authenticate(
        username: String,
        password: String,
        mtu: UShort = 1400u,
        encryptionMethod: EncryptionMethod = EncryptionMethod.XOR
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            // Transition to AUTH state
            _stateMachine.setState(ProtocolState.AUTH, "Starting authentication")

            // Create and connect socket with timeout
            socket = DatagramSocket().apply {
                soTimeout = AUTH_RETRY_TIMEOUT_MS.toInt() // Set socket timeout for receive operations
            }

            // Use the new decoupled authentication method
            val result = authenticateWithSocket(socket!!, username, password, mtu, encryptionMethod)

            if (result.success) {
                // Authentication successful - update state machine and start heartbeat
                _stateMachine.setState(ProtocolState.DATA, "Authentication successful")

                // Store session information in state machine
                result.sessionID?.let { sessionID ->
                    result.token?.let { token ->
                        _stateMachine.setSessionInfo(sessionID, token)
                    }
                }

                // Heartbeat is now handled by ConnectionManager's HeartbeatManager
                // No need to start duplicate heartbeat here
                true
            } else {
                // Authentication failed
                _stateMachine.setState(ProtocolState.AUTH_FAIL, result.errorMessage ?: "Authentication failed")
                false
            }

        } catch (e: Exception) {
            _stateMachine.setState(ProtocolState.AUTH_FAIL, "Authentication error: ${e.message}")
            throw e
        }
    }

    /**
     * NAME: disconnect
     *
     * DESCRIPTION:
     *     Disconnects from SDWAN server.
     *     Sends CLOSE packet and cleans up resources.
     */
    suspend fun disconnect() = withContext(Dispatchers.IO) {
        try {
            // Heartbeat is now handled by ConnectionManager's HeartbeatManager
            // No need to stop heartbeat here
            
            // Send CLOSE packet if connected
            if (_stateMachine.getState() == ProtocolState.DATA) {
                sendClosePacket()
            }
            
            // Close socket
            socket?.close()
            socket = null
            
            // Reset state machine
            _stateMachine.setState(ProtocolState.CLOSED, "Disconnected")
            _stateMachine.clearSession()
            
            // Clear encryption keys
            keyManager.clearKeys()
            
        } catch (e: Exception) {
            // Ignore errors during disconnect
        }
    }

    /**
     * NAME: sendDataPacket
     *
     * DESCRIPTION:
     *     Sends data packet through SDWAN tunnel.
     *     Encrypts data if encryption is enabled.
     *
     * PARAMETERS:
     *     data - IP packet data to send
     *
     * THROWS:
     *     IllegalStateException - If not connected
     *     Exception - If send fails
     */
    suspend fun sendDataPacket(data: ByteArray) = withContext(Dispatchers.IO) {
        require(stateMachine.getState() == ProtocolState.DATA) { 
            "Cannot send data packet: not connected" 
        }
        
        val (sessionID, token) = stateMachine.getSessionInfo()
        
        // Determine packet type based on encryption
        val packetType = if (encryptionService.getMethod() != EncryptionMethod.NONE) {
            PacketType.DATA_ENCRYPTED
        } else {
            PacketType.DATA
        }
        
        // Create packet header
        val header = PacketHeader(
            type = packetType,
            encrypt = encryptionService.getMethod(),
            sessionID = sessionID,
            token = token
        )
        
        // Encrypt data if needed
        val packetData = if (encryptionService.getMethod() != EncryptionMethod.NONE) {
            encryptionService.encrypt(data)
        } else {
            data
        }
        
        // Build and send packet (data packets don't have signatures)
        val packet = SDWANPacket(header, packetData)
        sendPacket(packet.toByteArray())
    }

    /**
     * NAME: getState
     *
     * DESCRIPTION:
     *     Gets current protocol state.
     *
     * RETURNS:
     *     ProtocolState - Current protocol state
     */
    fun getState(): ProtocolState = stateMachine.getState()

    /**
     * NAME: isConnected
     *
     * DESCRIPTION:
     *     Checks if protocol is in connected state.
     *
     * RETURNS:
     *     Boolean - true if connected, false otherwise
     */
    fun isConnected(): Boolean = stateMachine.getState() == ProtocolState.DATA

    /**
     * NAME: getSessionInfo
     *
     * DESCRIPTION:
     *     Gets current session information.
     *
     * RETURNS:
     *     Pair<UShort, UInt> - Session ID and token
     */
    fun getSessionInfo(): Pair<UShort, UInt> = stateMachine.getSessionInfo()

    /**
     * NAME: getEncryptionMethod
     *
     * DESCRIPTION:
     *     Gets current encryption method.
     *
     * RETURNS:
     *     EncryptionMethod - Current encryption method
     */
    fun getEncryptionMethod(): EncryptionMethod = encryptionService.getMethod()

    /**
     * NAME: buildOpenPacketForPing
     *
     * DESCRIPTION:
     *     Builds OPEN packet for ping testing without establishing connection.
     *     Used by ServerManager for lightweight ping operations.
     *
     * PARAMETERS:
     *     username - Username for packet
     *     password - Password for packet
     *     mtu - MTU value
     *     encryptionMethod - Encryption method
     *
     * RETURNS:
     *     ByteArray - OPEN packet data
     */
    suspend fun buildOpenPacketForPing(
        username: String,
        password: String,
        mtu: UShort,
        encryptionMethod: EncryptionMethod
    ): ByteArray {
        // Initialize encryption service for packet building
        initializeEncryption(username, password, encryptionMethod)

        // Build OPEN packet (same as authenticate method)
        return buildOpenPacket(username, password, mtu, encryptionMethod)
    }

    /**
     * NAME: cleanup
     *
     * DESCRIPTION:
     *     Cleans up protocol resources.
     *     Should be called when protocol is no longer needed.
     */
    fun cleanup() {
        scope.cancel()
        runBlocking {
            disconnect()
        }
    }

    // Private helper methods will be added in the next part
    private fun initializeEncryption(username: String, password: String, method: EncryptionMethod) {
        keyManager.generateSessionKey(username, password)
        
        _encryptionService = when (method) {
            EncryptionMethod.NONE -> NoEncryption()
            EncryptionMethod.XOR -> XOREncryption(keyManager)
            EncryptionMethod.AES -> AESEncryption(keyManager)
        }
    }

    private suspend fun buildOpenPacket(
        username: String,
        password: String,
        mtu: UShort,
        encryptionMethod: EncryptionMethod
    ): ByteArray {
        // Encrypt password using AES-ECB with MD5("mw" + username) key
        val encryptedPassword = PasswordEncryption.encryptPassword(password, username)

        // Build TLV attributes in required order (updated parameter order)
        // Include encryption method attribute to match iOS implementation
        val attributes = tlvParser.buildOpenPacketAttributes(
            username = username,
            encryptedPassword = encryptedPassword,
            mtu = mtu,
            encryptionMethod = encryptionMethod
        )
        
        // Create packet header
        val header = PacketHeader(
            type = PacketType.OPEN,
            encrypt = encryptionMethod,
            sessionID = 0u,
            token = 0u
        )
        
        // Build packet with signature
        val attributeData = tlvParser.buildAttributes(attributes)
        return PacketSignature.buildSignedPacket(header, attributeData)
    }

    /**
     * NAME: sendPacketWithSocket
     *
     * DESCRIPTION:
     *     Sends packet using external socket.
     *     Decoupled method for independent authentication and ping operations.
     *     Uses pre-resolved IP address to avoid DNS resolution in protocol layer.
     *
     * PARAMETERS:
     *     socket - External UDP socket to use
     *     packetData - Packet data to send
     *     targetIP - Pre-resolved IP address (optional, defaults to serverAddress for backward compatibility)
     */
    suspend fun sendPacketWithSocket(socket: DatagramSocket, packetData: ByteArray, targetIP: String? = null) {
        val address = if (targetIP != null) {
            try {
                InetAddress.getByName(targetIP)
            } catch (e: Exception) {
                // If resolved IP fails, fallback to original serverAddress
                logWarn("Failed to use resolved IP, falling back to serverAddress", mapOf(
                    "target_ip" to targetIP,
                    "server_address" to serverAddress,
                    "error" to (e.message ?: "unknown")
                ))
                InetAddress.getByName(serverAddress)
            }
        } else {
            // Fallback to original behavior for backward compatibility
            InetAddress.getByName(serverAddress)
        }
        val packet = DatagramPacket(packetData, packetData.size, address, serverPort)
        socket.send(packet)
    }

    /**
     * NAME: receiveAuthResponseWithSocket
     *
     * DESCRIPTION:
     *     Receives authentication response using external socket.
     *     Decoupled method for independent authentication.
     *
     * PARAMETERS:
     *     socket - External UDP socket to use
     *
     * RETURNS:
     *     SDWANPacket? - Received OPENACK packet or null if timeout/error
     */
    private suspend fun receiveAuthResponseWithSocket(socket: DatagramSocket): SDWANPacket? {
        return try {
            val buffer = ByteArray(4096)
            val packet = DatagramPacket(buffer, buffer.size)

            socket.receive(packet)
            val receivedData = buffer.copyOf(packet.length)

            // Parse packet
            val sdwanPacket = SDWANPacket.fromByteArray(receivedData)

            // Verify signature for non-data packets
            if (!PacketSignature.validatePacketSignature(receivedData, sdwanPacket.header)) {
                return null
            }

            sdwanPacket
        } catch (e: java.net.SocketTimeoutException) {
            // Socket timeout - server didn't respond within timeout period
            null
        } catch (e: Exception) {
            // Other network errors
            null
        }
    }

    /**
     * NAME: sendPacket
     *
     * DESCRIPTION:
     *     Sends packet using internal socket.
     *     Used by original methods for backward compatibility.
     *
     * PARAMETERS:
     *     packetData - Packet data to send
     */
    private suspend fun sendPacket(packetData: ByteArray) {
        socket?.let { sendPacketWithSocket(it, packetData) }
    }

    /**
     * NAME: receiveAuthResponse
     *
     * DESCRIPTION:
     *     Receives authentication response using internal socket.
     *     Used by original methods for backward compatibility.
     *
     * RETURNS:
     *     SDWANPacket? - Received OPENACK packet or null if timeout/error
     */
    private suspend fun receiveAuthResponse(): SDWANPacket? {
        return socket?.let { receiveAuthResponseWithSocket(it) }
    }

    /**
     * NAME: processOpenAckPacketForAuth
     *
     * DESCRIPTION:
     *     Processes authentication response packet (OPENACK or OPENREJECT).
     *     Decoupled from state machine for independent authentication.
     *
     * PARAMETERS:
     *     packet - SDWAN packet to process
     *     latencyMs - Round-trip time in milliseconds (optional)
     *
     * RETURNS:
     *     AuthenticationResult - Authentication result with session info, error details, and latency
     */
    private fun processOpenAckPacketForAuth(packet: SDWANPacket, latencyMs: Int? = null): AuthenticationResult {
        // logDebug("=== Processing authentication response packet ===")
        // logDebug("Packet type: ${packet.header.type}")

        return when (packet.header.type) {
            PacketType.OPEN_ACK -> {
                try {
                    // logDebug("Processing OPENACK packet")
                    // Extract signature and payload
                    val (signature, payload) = PacketSignature.extractSignature(packet.toByteArray(), packet.header)
                    // logDebug("Signature extracted successfully, payload size: ${payload.size}")

                    // Parse TLV attributes from payload
                    val attributes = tlvParser.parseAttributes(payload)
                    // logDebug("Parsed ${attributes.size} TLV attributes")

                    // Extract session information
                    val sessionID = packet.header.sessionID
                    val token = packet.header.token
                    // logDebug("Session info - ID: 0x${sessionID.toString(16)}, Token: 0x${token.toString(16)}")

                    // Process server configuration attributes and store locally
                    processServerAttributes(attributes)

                    // logDebug("Authentication result: SUCCESS")
                    AuthenticationResult(
                        success = true,
                        sessionID = sessionID,
                        token = token,
                        latencyMs = latencyMs
                    )
                } catch (e: Exception) {
                    logError("Failed to process OPENACK packet: ${e.message}", e)
                    AuthenticationResult(
                        success = false,
                        errorMessage = "Failed to process OPENACK packet: ${e.message}"
                    )
                }
            }

            PacketType.OPEN_REJECT -> {
                try {
                    // logDebug("Processing OPENREJECT packet")
                    val errorInfo = processOpenRejectPacket(packet)
                    logError("Authentication rejected by server: ${errorInfo.errorMessage}")

                    AuthenticationResult(
                        success = false,
                        errorMessage = errorInfo.errorMessage
                    )
                } catch (e: Exception) {
                    logError("Failed to process OPENREJECT packet: ${e.message}", e)
                    AuthenticationResult(
                        success = false,
                        errorMessage = "Authentication rejected by server"
                    )
                }
            }

            else -> {
                logError("Invalid packet type for authentication response: expected OPEN_ACK or OPEN_REJECT, got ${packet.header.type}")
                AuthenticationResult(
                    success = false,
                    errorMessage = "Invalid authentication response packet type"
                )
            }
        }
    }

    /**
     * NAME: processOpenRejectPacket
     *
     * DESCRIPTION:
     *     Processes OPENREJECT packet to extract rejection reason and error code.
     *     Compatible with Go backend and iOS implementation.
     *
     * PARAMETERS:
     *     packet - SDWAN packet to process
     *
     * RETURNS:
     *     OpenRejectInfo - Rejection information with error code and message
     */
    private fun processOpenRejectPacket(packet: SDWANPacket): OpenRejectInfo {
        try {
            val packetData = packet.toByteArray()

            // Skip packet header (8 bytes) to get to payload
            val payloadOffset = 8
            if (packetData.size <= payloadOffset) {
                return OpenRejectInfo(0, "Unknown rejection reason")
            }

            val payload = packetData.sliceArray(payloadOffset until packetData.size)

            // Skip MD5 signature (first 16 bytes of payload)
            val signatureSize = 16
            if (payload.size <= signatureSize) {
                return OpenRejectInfo(0, "Unknown rejection reason")
            }

            // Extract rejection reason code (first byte after signature)
            val reasonCode = payload[signatureSize].toInt() and 0xFF
            val errorMessage = getRejectReasonString(reasonCode)

            return OpenRejectInfo(reasonCode, errorMessage)

        } catch (e: Exception) {
            logError("Failed to parse OPENREJECT packet: ${e.message}", e)
            return OpenRejectInfo(0, "Failed to parse rejection reason")
        }
    }

    /**
     * NAME: getRejectReasonString
     *
     * DESCRIPTION:
     *     Converts rejection reason code to human-readable message.
     *     Compatible with Go backend constants.
     *
     * PARAMETERS:
     *     reasonCode - Rejection reason code
     *
     * RETURNS:
     *     String - Human-readable error message
     */
    private fun getRejectReasonString(reasonCode: Int): String {
        return when (reasonCode) {
            1 -> "Invalid username"
            2 -> "Invalid password"
            3 -> "Server is full"
            4 -> "Server error"
            5 -> "Unsupported feature"
            6 -> "Account expired"
            7 -> "Account disabled"
            8 -> "Maximum sessions reached"
            9 -> "Invalid token"
            else -> "Unknown rejection reason"
        }
    }

    /**
     * NAME: isCredentialRelatedError
     *
     * DESCRIPTION:
     *     判断错误消息是否为凭证相关错误（来自OPENREJECT包）
     *     这些错误不应该重试，因为重试也会失败
     *
     * PARAMETERS:
     *     errorMessage - 错误消息
     *
     * RETURNS:
     *     Boolean - 如果是凭证相关错误返回true
     */
    private fun isCredentialRelatedError(errorMessage: String?): Boolean {
        if (errorMessage == null) return false

        val lowerMessage = errorMessage.lowercase()
        val credentialErrors = listOf(
            "invalid username",
            "invalid password",
            "account expired",
            "account disabled",
            "invalid token"
        )

        return credentialErrors.any { error -> lowerMessage.contains(error) }
    }

    /**
     * NAME: OpenRejectInfo
     *
     * DESCRIPTION:
     *     Data class for OPENREJECT packet information.
     *
     * PROPERTIES:
     *     errorCode - Rejection reason code
     *     errorMessage - Human-readable error message
     */
    private data class OpenRejectInfo(
        val errorCode: Int,
        val errorMessage: String
    )

    /**
     * NAME: processOpenAckPacket
     *
     * DESCRIPTION:
     *     Processes OPENACK packet and updates state machine.
     *     Used by the original authenticate method for backward compatibility.
     *
     * PARAMETERS:
     *     packet - SDWAN packet to process
     *
     * RETURNS:
     *     Boolean - true if processing successful, false otherwise
     */
    private fun processOpenAckPacket(packet: SDWANPacket): Boolean {
        val result = processOpenAckPacketForAuth(packet)

        if (result.success) {
            // Store session information in state machine
            result.sessionID?.let { sessionID ->
                result.token?.let { token ->
                    stateMachine.setSessionInfo(sessionID, token)
                }
            }
            return true
        }

        return false
    }

    private fun processServerAttributes(attributes: List<TLVAttribute>) {
        // Process server-provided configuration
        // logDebug("Processing ${attributes.size} server attributes")
        for (attribute in attributes) {
            // logDebug("Processing attribute: type=${attribute.type}, value_size=${attribute.value.size}, value_hex=${attribute.value.joinToString("") { "%02x".format(it) }}")
            when (attribute.type) {
                TLVAttributeType.IP -> {
                    // Server assigned IP address
                    val ip = tlvParser.extractIPAddress(attribute)
                    if (ip != null) {
                        tunnelIP = ip
                        // logInfo("Tunnel IP assigned: $tunnelIP")
                    } else {
                        logWarn("Invalid IP attribute: size=${attribute.value.size}, expected 4 bytes")
                    }
                }
                TLVAttributeType.GATEWAY -> {
                    // Gateway IP address
                    val gateway = tlvParser.extractIPAddress(attribute)
                    if (gateway != null) {
                        gatewayIP = gateway
                        // logInfo("Gateway IP assigned: $gatewayIP")
                    } else {
                        logWarn("Invalid GATEWAY attribute: size=${attribute.value.size}, expected 4 bytes")
                    }
                }
                TLVAttributeType.DNS -> {
                    // DNS server address
                    val dns = tlvParser.extractIPAddress(attribute)
                    if (dns != null) {
                        dnsIP = dns
                        // logInfo("DNS IP assigned: $dnsIP")
                    } else {
                        logWarn("Invalid DNS attribute: size=${attribute.value.size}, expected 4 bytes, skipping")
                    }
                }
                TLVAttributeType.MTU -> {
                    // Server MTU setting
                    mtuValue = tlvParser.extractIntValue(attribute)
                    // logInfo("MTU assigned: $mtuValue")
                }
                else -> {
                    // Other attributes can be processed as needed
                    // logDebug("Unhandled server attribute: ${attribute.type}")
                }
            }
        }
    }

    // Heartbeat functionality moved to ConnectionManager's HeartbeatManager
    // This eliminates duplicate heartbeat implementations and prevents conflicts

    // sendHeartbeatPacket method removed - heartbeat functionality moved to HeartbeatManager

    private suspend fun sendClosePacket() {
        val (sessionID, token) = stateMachine.getSessionInfo()

        // Create CLOSE packet header
        val header = PacketHeader(
            type = PacketType.CLOSE,
            encrypt = encryptionService.getMethod(),
            sessionID = sessionID,
            token = token
        )

        // Build packet with signature (no payload for CLOSE packets)
        val packetData = PacketSignature.buildSignedPacket(header, ByteArray(0))
        sendPacket(packetData)
    }

}
